<template>
  <van-button
    :type="buttonType"
    :size="size"
    :disabled="disabled || loading"
    :loading="loading"
    :block="block"
    :round="round"
    :square="square"
    @click="handleClick"
  >
    <slot />
  </van-button>
</template>

<script setup lang="ts">
interface Props {
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'default'
  size?: 'large' | 'normal' | 'small' | 'mini'
  disabled?: boolean
  loading?: boolean
  block?: boolean
  round?: boolean
  square?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  size: 'normal',
  disabled: false,
  loading: false,
  block: false,
  round: false,
  square: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonType = computed(() => {
  const typeMap = {
    primary: 'primary',
    success: 'success',
    warning: 'warning',
    danger: 'danger',
    default: 'default'
  }
  return typeMap[props.type]
})

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>