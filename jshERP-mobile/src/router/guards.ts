import type { Router } from 'vue-router'
import { useAuthStore } from '@/stores'
import { useAppStore } from '@/stores'

export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()
    const appStore = useAppStore()
    
    // 显示加载状态
    appStore.setLoading(true)
    
    // 检查认证状态
    if (to.meta.requiresAuth && !authStore.isLoggedIn) {
      next('/auth/login')
      return
    }
    
    // 已登录用户访问登录页，重定向到仪表板
    if (to.path === '/auth/login' && authStore.isLoggedIn) {
      next('/dashboard')
      return
    }
    
    // 检查权限
    if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
      const hasPermission = to.meta.permissions.some(
        (permission: string) => authStore.checkPermission(permission)
      )
      if (!hasPermission) {
        // TODO: 跳转到403页面
        console.warn('No permission to access:', to.path)
        next('/dashboard')
        return
      }
    }
    
    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - jshERP移动端`
    }
    
    next()
  })
  
  // 全局后置守卫
  router.afterEach(() => {
    const appStore = useAppStore()
    appStore.setLoading(false)
  })
  
  // 全局解析守卫
  router.beforeResolve(async (to) => {
    // 可以在这里进行一些异步操作
    console.log('Navigating to:', to.path)
  })
}