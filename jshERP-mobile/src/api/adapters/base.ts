import { httpClient } from '@/utils/request'

export abstract class BaseAdapter<TRequest, TResponse> {
  protected abstract endpoint: string
  
  abstract transform(rawData: any): TResponse
  
  async request(params: TRequest): Promise<TResponse> {
    try {
      const response = await httpClient.post(this.endpoint, params)
      return this.transform(response)
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }
  
  async get(params?: any): Promise<TResponse> {
    try {
      const response = await httpClient.get(this.endpoint, { params })
      return this.transform(response)
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }
  
  async post(data: TRequest): Promise<TResponse> {
    try {
      const response = await httpClient.post(this.endpoint, data)
      return this.transform(response)
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }
  
  async put(data: TRequest): Promise<TResponse> {
    try {
      const response = await httpClient.put(this.endpoint, data)
      return this.transform(response)
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }
  
  async delete(id: string | number): Promise<void> {
    try {
      await httpClient.delete(`${this.endpoint}/${id}`)
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }
  
  protected handleError(error: any): void {
    console.error(`API Error [${this.endpoint}]:`, error)
    
    // 可以在这里添加错误上报逻辑
    if (error.response) {
      // 服务器响应错误
      console.error('Response error:', error.response.data)
    } else if (error.request) {
      // 网络错误
      console.error('Network error:', error.request)
    } else {
      // 其他错误
      console.error('Error:', error.message)
    }
  }
}