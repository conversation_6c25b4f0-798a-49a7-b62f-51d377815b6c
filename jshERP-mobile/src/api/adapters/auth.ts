import { BaseAdapter } from './base'
import { httpClient } from '@/utils/request'
import type { LoginCredentials, UserInfo } from '@/stores/modules/auth'

export interface LoginResponse {
  token: string
  refreshToken?: string
  userInfo: UserInfo
  permissions: string[]
  expiresIn: number
  tenantId: number
}

export interface CaptchaResponse {
  captchaId: string
  captchaImage: string
}

export class AuthAdapter extends BaseAdapter<LoginCredentials, LoginResponse> {
  protected endpoint = '/user'

  transform(rawData: any): LoginResponse {
    // jshERP响应格式适配
    const data = rawData.data || rawData
    const userInfo = data.userInfo || data

    return {
      token: data.token || data.access_token,
      refreshToken: data.refreshToken,
      userInfo: {
        id: userInfo.id || userInfo.userId,
        username: userInfo.username || userInfo.loginName,
        realname: userInfo.realname || userInfo.name || userInfo.username,
        avatar: userInfo.avatar || userInfo.headImg || '/default-avatar.png',
        email: userInfo.email,
        phone: userInfo.phone || userInfo.phoneNumber
      },
      permissions: data.permissions || data.authorities || [],
      expiresIn: data.expiresIn || 7200,
      tenantId: data.tenantId || userInfo.tenantId || 1
    }
  }
  
  /**
   * 获取验证码
   */
  async getCaptcha(): Promise<CaptchaResponse> {
    try {
      const response = await httpClient.get(`${this.endpoint}/randomImage`)

      // jshERP返回格式：{code: 200, data: {base64: "data:image/svg+xml;base64,...", uuid: "..."}}
      const data = response.data || response

      return {
        captchaId: data.uuid || Date.now().toString(),
        captchaImage: data.base64 || data.data?.base64
      }
    } catch (error) {
      console.error('获取验证码失败:', error)
      throw new Error('获取验证码失败，请重试')
    }
  }

  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials & { captcha: string }): Promise<LoginResponse> {
    try {
      const response = await httpClient.post(`${this.endpoint}/login`, {
        username: credentials.username,
        password: credentials.password,
        captcha: credentials.captcha
      })

      // 检查jshERP响应格式
      if (response.code && response.code !== 200) {
        throw new Error(response.data?.message || response.message || '登录失败')
      }

      return this.transform(response)
    } catch (error: any) {
      console.error('登录失败:', error)

      // 处理jshERP特定错误
      if (error.response?.data?.code === 500012) {
        throw new Error('验证码错误或已过期')
      } else if (error.response?.data?.code === 500011) {
        throw new Error('用户名或密码错误')
      } else if (error.message) {
        throw new Error(error.message)
      } else {
        throw new Error('登录失败，请检查网络连接')
      }
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      await httpClient.get(`${this.endpoint}/logout`)
    } catch (error) {
      console.error('登出失败:', error)
      // 即使登出失败也要清除本地状态
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<UserInfo> {
    try {
      const response = await httpClient.get(`${this.endpoint}/getUserSession`)
      const data = response.data || response

      return {
        id: data.id || data.userId,
        username: data.username || data.loginName,
        realname: data.realname || data.name || data.username,
        avatar: data.avatar || data.headImg || '/default-avatar.png',
        email: data.email,
        phone: data.phone || data.phoneNumber
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw new Error('获取用户信息失败')
    }
  }
  
  async getCurrentUser(): Promise<UserInfo> {
    const response = await httpClient.get('/sys/user/getUserInfo') as any
    return {
      id: response.result?.id || response.id,
      username: response.result?.username || response.username,
      realname: response.result?.realname || response.realname,
      avatar: response.result?.avatar || response.avatar || '/default-avatar.png',
      email: response.result?.email || response.email,
      phone: response.result?.phone || response.phone
    }
  }

  /**
   * 获取用户权限
   */
  async getPermissions(): Promise<string[]> {
    try {
      // jshERP可能没有专门的权限API，暂时返回默认权限
      return ['dashboard:view', 'sales:manage', 'purchase:manage', 'inventory:manage']
    } catch (error) {
      console.error('获取权限失败:', error)
      return []
    }
  }
}

export const authAdapter = new AuthAdapter()