<!--
  ERP首页仪表板

  包含数据卡片、快捷功能和统计面板
-->
<template>
  <div class="erp-page">
    <!-- 头部区域 -->
    <div class="erp-header">
      <h1 class="erp-header__title">聆听东方之美</h1>
      <p class="erp-header__subtitle">传承匠心工艺</p>
    </div>

    <!-- 数据卡片区域 -->
    <div class="dashboard-cards">
      <ERPDataCard
        v-for="card in dataCards"
        :key="card.key"
        :value="card.value"
        :label="card.label"
        :type="card.type"
        :show-refresh="card.showRefresh"
        :refreshing="refreshing"
        @click="handleCardClick(card)"
        @refresh="handleRefresh"
      />
    </div>

    <!-- 快捷功能区域 -->
    <div class="dashboard-quick">
      <ERPFunctionGrid
        :items="quickActions"
        :columns="3"
        @item-click="handleQuickAction"
      />
    </div>

    <!-- 统计数据区域 -->
    <div class="dashboard-stats">
      <ERPStatPanel :data="statisticsData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Toast } from 'vant'
import ERPDataCard from '@/components/erp/ERPDataCard.vue'
import ERPFunctionGrid from '@/components/erp/ERPFunctionGrid.vue'
import ERPStatPanel from '@/components/erp/ERPStatPanel.vue'

/**
 * 数据卡片配置接口
 */
interface DataCard {
  key: string
  value?: number | string
  label: string
  type?: 'number' | 'currency'
  showRefresh?: boolean
}

/**
 * 快捷功能接口
 */
interface QuickAction {
  id: string
  icon: string
  label: string
  color: string
  path?: string
}

/**
 * 统计数据接口
 */
interface StatisticsData {
  yesterday: { sales: number; retail: number; purchase: number }
  monthly: { sales: number; retail: number; purchase: number }
  yearly: { sales: number; retail: number; purchase: number }
  summary: { inventoryTotal: number; accountTotal: number; yearlyProfit: number }
}

const router = useRouter()
const refreshing = ref(false)

/**
 * 仪表板数据
 */
const dashboardData = reactive({
  todaySales: 0,
  todayRetail: 0,
  todayProfit: 0,
  receivables: 0,
  todayPurchase: 0
})

/**
 * 数据卡片配置
 */
const dataCards = ref<DataCard[]>([
  { key: 'todaySales', value: dashboardData.todaySales, label: '今日销售', type: 'number' },
  { key: 'refresh', label: '刷新', showRefresh: true },
  { key: 'todayRetail', value: dashboardData.todayRetail, label: '今日零售', type: 'number' },
  { key: 'todayProfit', value: dashboardData.todayProfit, label: '今日毛利', type: 'currency' },
  { key: 'receivables', value: dashboardData.receivables, label: '应收账款', type: 'currency' },
  { key: 'todayPurchase', value: dashboardData.todayPurchase, label: '今日采购', type: 'number' }
])

/**
 * 快捷功能配置
 */
const quickActions: QuickAction[] = [
  { id: 'messages', icon: 'bell-o', label: '消息中心', color: '#4A90E2', path: '/messages' },
  { id: 'receivables', icon: 'calendar-o', label: '应收应付', color: '#4A90E2', path: '/receivables' },
  { id: 'inventory-alert', icon: 'warning-o', label: '库存预警', color: '#4A90E2', path: '/inventory-alert' }
]

/**
 * 统计数据
 */
const statisticsData = reactive<StatisticsData>({
  yesterday: { sales: 5, retail: 3, purchase: 2 },
  monthly: { sales: 150, retail: 89, purchase: 45 },
  yearly: { sales: 1800, retail: 1200, purchase: 600 },
  summary: {
    inventoryTotal: 500000,
    accountTotal: 300000,
    yearlyProfit: 120000
  }
})

/**
 * 获取仪表板数据
 */
const fetchDashboardData = async (): Promise<void> => {
  try {
    // TODO: 调用真实API
    // const data = await dashboardApi.getData()

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟数据更新
    Object.assign(dashboardData, {
      todaySales: Math.floor(Math.random() * 10),
      todayRetail: Math.floor(Math.random() * 8),
      todayProfit: Math.floor(Math.random() * 5000),
      receivables: Math.floor(Math.random() * 50000),
      todayPurchase: Math.floor(Math.random() * 6)
    })

    // 更新卡片数据
    updateDataCards()

    Toast.success('数据已更新')
  } catch (error) {
    Toast.fail('数据获取失败')
    console.error('Dashboard data fetch error:', error)
  }
}

/**
 * 更新数据卡片
 */
const updateDataCards = (): void => {
  dataCards.value = dataCards.value.map(card => {
    if (card.key in dashboardData) {
      return { ...card, value: dashboardData[card.key as keyof typeof dashboardData] }
    }
    return card
  })
}

/**
 * 处理卡片点击
 */
const handleCardClick = (card: DataCard): void => {
  console.log('Card clicked:', card)
  // TODO: 根据卡片类型跳转到对应页面
}

/**
 * 处理刷新
 */
const handleRefresh = async (): Promise<void> => {
  if (refreshing.value) return

  refreshing.value = true
  try {
    await fetchDashboardData()
  } finally {
    refreshing.value = false
  }
}

/**
 * 处理快捷功能点击
 */
const handleQuickAction = (action: QuickAction): void => {
  console.log('Quick action clicked:', action)
  if (action.path) {
    // TODO: 跳转到对应页面
    Toast(`点击了${action.label}`)
  }
}

/**
 * 组件挂载时获取数据
 */
onMounted(() => {
  fetchDashboardData()
})
</script>

<style scoped lang="less">
@import '@/styles/variables.less';
@import '@/styles/common.less';

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--erp-spacing-md);
  padding: var(--erp-spacing-md);

  // 响应式调整
  @media (max-width: @erp-mobile-xs) {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--erp-spacing-sm);
    padding: var(--erp-spacing-sm);
  }
}

.dashboard-quick {
  margin: var(--erp-spacing-md) 0;

  .erp-card {
    margin: var(--erp-spacing-md);

    @media (max-width: @erp-mobile-xs) {
      margin: var(--erp-spacing-sm);
    }
  }
}

.dashboard-stats {
  margin-bottom: var(--erp-spacing-lg);
}
</style>
