<!--
  个人中心页面

  包含用户信息卡片、功能菜单和底部提示
-->
<template>
  <div class="erp-page erp-page--no-padding">
    <!-- 用户信息卡片 -->
    <ERPUserCard :user-info="userInfo" :trial-info="trialInfo" />

    <!-- 功能菜单列表 -->
    <div class="profile-menu">
      <ERPMenuList :items="menuItems" @item-click="handleMenuClick" />
    </div>

    <!-- 底部提示已移除 -->
  </div>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showDialog, showToast } from 'vant'
import { useAuthStore } from '@/stores'
import ERPUserCard from '@/components/erp/ERPUserCard.vue'
import ERPMenuList from '@/components/erp/ERPMenuList.vue'
import type { UserInfo, TrialInfo, MenuItem } from '@/types/erp'

const router = useRouter()
const authStore = useAuthStore()

/**
 * 用户信息 - 从认证store获取真实数据
 */
const userInfo = computed<UserInfo>(() => {
  const user = authStore.userInfo
  console.log('Current user from store:', user) // 调试日志

  return {
    username: user?.realname || user?.username || '未知用户',
    userType: getUserType(),
    avatar: user?.avatar || '' // 可以设置头像URL
  }
})

/**
 * 获取用户类型描述
 */
const getUserType = (): string => {
  // 从localStorage获取完整的用户数据
  try {
    const accessToken = localStorage.getItem('ACCESS_TOKEN')
    if (!accessToken) return '用户'

    // 从token中解析用户信息，或者使用存储的用户数据
    const rawUserInfo = localStorage.getItem('USER_INFO')
    if (rawUserInfo) {
      const rawUser = JSON.parse(rawUserInfo)
      console.log('Raw user data:', rawUser) // 调试日志

      // 根据用户角色返回描述
      if (rawUser.ismanager === 1) return '管理员'
      if (rawUser.isystem === 1) return '系统管理员'
      if (rawUser.position) return rawUser.position
      if (rawUser.department) return rawUser.department

      // 根据用户名判断（admin通常是管理员）
      if (rawUser.loginName === 'admin' || rawUser.username === 'admin') {
        return '系统管理员'
      }
    }
  } catch (error) {
    console.error('Parse user info error:', error)
  }

  return '员工'
}

/**
 * 试用信息 - 移除硬编码，根据实际需要可连接API
 */
const trialInfo = reactive<TrialInfo>({
  isTrialUser: false, // 默认不显示试用信息
  endDate: '',
  currentUsers: 0,
  maxUsers: 0
})

/**
 * 功能菜单配置
 */
const menuItems: MenuItem[] = [
  { id: 'about', icon: 'info-o', label: '关于我们' },
  { id: 'password', icon: 'lock', label: '登录密码' },
  { id: 'cache', icon: 'delete-o', label: '缓存链接' },
  { id: 'profile', icon: 'contact', label: '个人信息' },
  { id: 'logout', icon: 'sign-out', label: '退出登录', danger: true }
]

/**
 * 处理菜单项点击
 */
const handleMenuClick = async (item: MenuItem): Promise<void> => {
  console.log('Menu item clicked:', item)

  switch (item.id) {
    case 'about':
      await handleAbout()
      break

    case 'password':
      await handleChangePassword()
      break

    case 'cache':
      await handleClearCache()
      break

    case 'profile':
      await handleEditProfile()
      break

    case 'logout':
      await handleLogout()
      break

    default:
      showToast(`点击了${item.label}`)
      break
  }
}

/**
 * 关于我们
 */
const handleAbout = async (): Promise<void> => {
  try {
    // 显示系统信息
    await showDialog({
      title: '关于聆花文化ERP系统',
      message: `
        系统名称：聆花文化ERP系统
        版本：V3.5 移动端
        开发：聆花文化团队

        聆花文化ERP系统是专为文化艺术企业
        打造的综合管理平台，专注于珐琅、
        艺术品等文化产品的进销存管理。
      `,
      confirmButtonText: '确定'
    })
  } catch (error) {
    console.log('About dialog cancelled')
  }
}

/**
 * 修改密码
 */
const handleChangePassword = async (): Promise<void> => {
  try {
    showToast('密码修改功能开发中...')
    // TODO: 实现密码修改功能
    // 可以跳转到密码修改页面或显示密码修改对话框
  } catch (error) {
    console.error('Change password error:', error)
    showToast({ type: 'fail', message: '密码修改失败' })
  }
}

/**
 * 清理缓存
 */
const handleClearCache = async (): Promise<void> => {
  try {
    await showDialog({
      title: '清理缓存',
      message: '确定要清理应用缓存吗？这将清除本地存储的数据。',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    // 清理localStorage中的缓存数据
    const keysToKeep = ['auth_token', 'user_info'] // 保留认证信息
    const allKeys = Object.keys(localStorage)

    allKeys.forEach(key => {
      if (!keysToKeep.includes(key)) {
        localStorage.removeItem(key)
      }
    })

    showToast({ type: 'success', message: '缓存清理完成' })
  } catch (error) {
    console.log('Clear cache cancelled')
  }
}

/**
 * 编辑个人信息
 */
const handleEditProfile = async (): Promise<void> => {
  try {
    showToast('个人信息编辑功能开发中...')
    // TODO: 实现个人信息编辑功能
    // 可以跳转到个人信息编辑页面
  } catch (error) {
    console.error('Edit profile error:', error)
    showToast({ type: 'fail', message: '个人信息编辑失败' })
  }
}

/**
 * 处理退出登录
 */
const handleLogout = async (): Promise<void> => {
  try {
    await showDialog({
      title: '退出登录',
      message: '确定要退出登录吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    // 执行退出登录
    await authStore.logout()
    showToast({ type: 'success', message: '已退出登录' })

    // 跳转到登录页面
    router.push('/auth/login')
  } catch (error) {
    // 用户取消或退出失败
    console.log('Logout cancelled or failed:', error)
  }
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';
@import '@/styles/common.less';

.erp-page--no-padding {
  padding-bottom: 60px; // Tab栏高度

  .erp-header {
    margin: 0;
  }
}

.profile-menu {
  margin-top: var(--erp-spacing-lg);
}

.profile-footer {
  margin: var(--erp-spacing-xl) var(--erp-spacing-md) var(--erp-spacing-lg);
  text-align: center;

  &__text {
    font-size: var(--erp-font-size-xs);
    color: var(--erp-text-tertiary);
    line-height: var(--erp-line-height-normal);
    margin: 0;
  }
}

// 响应式适配
@media (max-width: @erp-mobile-xs) {
  .profile-footer {
    margin: var(--erp-spacing-lg) var(--erp-spacing-sm) var(--erp-spacing-md);

    &__text {
      font-size: 10px;
    }
  }
}
</style>
