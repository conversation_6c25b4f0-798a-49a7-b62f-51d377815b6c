<template>
  <div class="sales-page">
    <van-search
      v-model="searchValue"
      placeholder="搜索订单"
      @search="onSearch"
    />
    
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <van-cell
          v-for="item in salesList"
          :key="item.id"
          :title="item.orderNo"
          :label="`客户：${item.customerName}`"
          :value="`¥${item.totalAmount}`"
          is-link
          @click="viewOrder(item)"
        />
      </van-list>
    </van-pull-refresh>
    
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="createOrder"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface SalesItem {
  id: number
  orderNo: string
  customerName: string
  totalAmount: number
}


const searchValue = ref('')
const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)
const salesList = ref<SalesItem[]>([])

const onSearch = (value: string) => {
  console.log('Search:', value)
}

const onRefresh = async () => {
  // 重置数据
  salesList.value = []
  finished.value = false
  await onLoad()
  refreshing.value = false
}

const onLoad = async () => {
  // 模拟加载数据
  setTimeout(() => {
    for (let i = 0; i < 10; i++) {
      salesList.value.push({
        id: salesList.value.length + 1,
        orderNo: `SO${String(salesList.value.length + 1).padStart(6, '0')}`,
        customerName: `客户${salesList.value.length + 1}`,
        totalAmount: Math.floor(Math.random() * 10000) + 1000
      })
    }
    loading.value = false
    
    if (salesList.value.length >= 50) {
      finished.value = true
    }
  }, 1000)
}

const viewOrder = (order: SalesItem) => {
  console.log('View order:', order)
}

const createOrder = () => {
  console.log('Create new order')
}

onMounted(() => {
  onLoad()
})
</script>

<style scoped>
.sales-page {
  height: 100%;
}
</style>