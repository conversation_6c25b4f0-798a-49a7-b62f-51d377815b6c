<template>
  <div class="inventory-page">
    <van-search
      v-model="searchValue"
      placeholder="搜索商品"
      @search="onSearch"
    />
    
    <van-tabs v-model:active="activeTab">
      <van-tab title="库存列表">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <van-cell
              v-for="item in inventoryList"
              :key="item.id"
              :title="item.productName"
              :label="`库存：${item.stock}`"
              :value="item.status"
              is-link
              @click="viewProduct(item)"
            >
              <template #right-icon>
                <van-tag
                  :type="item.stock < 10 ? 'danger' : 'success'"
                >
                  {{ item.stock < 10 ? '预警' : '正常' }}
                </van-tag>
              </template>
            </van-cell>
          </van-list>
        </van-pull-refresh>
      </van-tab>
      
      <van-tab title="盘点记录">
        <van-empty description="暂无盘点记录" />
      </van-tab>
    </van-tabs>
    
    <van-floating-bubble
      axis="xy"
      icon="scan"
      @click="startScan"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface InventoryItem {
  id: number
  productName: string
  stock: number
  status: string
}

const searchValue = ref('')
const activeTab = ref(0)
const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)
const inventoryList = ref<InventoryItem[]>([])

const onSearch = (value: string) => {
  console.log('Search:', value)
}

const onRefresh = async () => {
  inventoryList.value = []
  finished.value = false
  await onLoad()
  refreshing.value = false
}

const onLoad = async () => {
  setTimeout(() => {
    for (let i = 0; i < 10; i++) {
      inventoryList.value.push({
        id: inventoryList.value.length + 1,
        productName: `商品${inventoryList.value.length + 1}`,
        stock: Math.floor(Math.random() * 100),
        status: '在库'
      })
    }
    loading.value = false
    
    if (inventoryList.value.length >= 50) {
      finished.value = true
    }
  }, 1000)
}

const viewProduct = (product: InventoryItem) => {
  console.log('View product:', product)
}

const startScan = () => {
  console.log('Start scanning')
}

onMounted(() => {
  onLoad()
})
</script>

<style scoped>
.inventory-page {
  height: 100%;
}
</style>