<!--
  新增销售订单页面

  功能：
  - 订单基本信息录入
  - 商品选择和添加
  - 金额计算
  - 保存订单
-->
<template>
  <div class="sales-order-add">
    <!-- 页面头部 -->
    <div class="page-header">
      <van-nav-bar
        title="新增销售订单"
        left-arrow
        @click-left="handleBack"
        class="nav-bar"
      >
        <template #right>
          <div class="header-actions">
            <van-icon
              name="question-o"
              class="help-icon"
              @click="showHelp"
            />
          </div>
        </template>
      </van-nav-bar>

      <!-- 页面状态指示器 -->
      <div class="page-status">
        <div class="status-item active">
          <div class="status-dot"></div>
          <span class="status-text">填写信息</span>
        </div>
        <div class="status-line"></div>
        <div class="status-item">
          <div class="status-dot"></div>
          <span class="status-text">保存订单</span>
        </div>
      </div>
    </div>

    <!-- 订单表单容器 -->
    <div class="form-container">
      <BaseOrderForm
        ref="orderFormRef"
        :show-validation="showValidation"
        @scan-product="handleScanProduct"
        @select-product="handleSelectProduct"
        class="order-form"
      />
    </div>

    <!-- 底部操作栏 -->
    <div class="action-bar">
      <van-button
        type="success"
        size="large"
        :loading="submitting"
        :disabled="!isFormValid"
        @click="handleSaveAndSubmit"
        class="action-button primary-action"
      >
        <van-icon name="success" />
        保存并提交
      </van-button>
      <van-button
        type="primary"
        size="large"
        :loading="submitting"
        @click="handleSave"
        class="action-button secondary-action"
      >
        <van-icon name="edit" />
        保存草稿
      </van-button>
    </div>

    <!-- 帮助弹窗 -->
    <van-popup
      v-model:show="showHelpDialog"
      position="center"
      class="help-popup"
      :close-on-click-overlay="true"
      round
    >
      <div class="help-content">
        <div class="help-header">
          <h3>操作说明</h3>
          <van-icon
            name="cross"
            class="close-icon"
            @click="showHelpDialog = false"
          />
        </div>

        <div class="help-body">
          <div class="help-item">
            <div class="help-icon">
              <van-icon name="user-o" />
            </div>
            <div class="help-text">
              <h4>1. 基础信息</h4>
              <p>请依次选择客户、订单日期和销售人员，这些为必填项</p>
            </div>
          </div>

          <div class="help-item">
            <div class="help-icon">
              <van-icon name="shopping-cart-o" />
            </div>
            <div class="help-text">
              <h4>2. 商品清单</h4>
              <p>可通过扫描条码或手动选择的方式添加商品</p>
            </div>
          </div>

          <div class="help-item">
            <div class="help-icon">
              <van-icon name="completed" />
            </div>
            <div class="help-text">
              <h4>3. 保存选项</h4>
              <p>• 保存草稿：保存当前内容，可稍后继续编辑</p>
              <p>• 保存并提交：完成订单并提交审核</p>
            </div>
          </div>
        </div>

        <div class="help-footer">
          <van-button
            type="primary"
            block
            round
            @click="showHelpDialog = false"
          >
            我知道了
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import BaseOrderForm from '@/components/order/BaseOrderForm.vue'
import { useOrderForm } from '@/composables/useOrderForm'
import { useSalesAPI } from '@/composables/useSalesAPI'
import type { OrderAction } from '@/types/order'

const router = useRouter()

// 使用订单表单Hook
const {
  orderForm,
  submitting,
  isFormValid,
  validateForm,
  initializeForm
} = useOrderForm()

// 使用销售API Hook
const {
  createSalesOrder,
  submitting: apiSubmitting
} = useSalesAPI()

// 页面状态
const showValidation = ref<boolean>(false)
const showHelpDialog = ref<boolean>(false)
const orderFormRef = ref()
const hasUnsavedChanges = ref<boolean>(false)

/**
 * 返回处理
 */
const handleBack = async (): Promise<void> => {
  if (hasUnsavedChanges.value) {
    try {
      await showConfirmDialog({
        title: '确认离开',
        message: '当前有未保存的内容，确定要离开吗？'
      })
    } catch {
      return // 用户取消
    }
  }
  router.back()
}

/**
 * 显示帮助
 */
const showHelp = (): void => {
  showHelpDialog.value = true
}

/**
 * 扫描商品
 */
const handleScanProduct = (): void => {
  // TODO: 实现扫码功能
  showToast('扫码功能开发中')
  hasUnsavedChanges.value = true
}

/**
 * 选择商品
 */
const handleSelectProduct = (): void => {
  // TODO: 实现商品选择功能
  showToast('商品选择功能开发中')
  hasUnsavedChanges.value = true
}

/**
 * 移除商品
 */
const handleRemoveProduct = (index: number): void => {
  orderForm.products.splice(index, 1)
  calculateAmount()
}

/**
 * 优惠率变化
 */
const handleDiscountChange = (): void => {
  calculateAmount()
}

/**
 * 文件超大小
 */
const handleOversizeFile = (): void => {
  showToast('文件大小不能超过1M')
}

/**
 * 保存草稿
 */
const handleSave = async (): Promise<void> => {
  showValidation.value = true

  if (!validateForm()) {
    return
  }

  submitting.value = true

  try {
    // TODO: 调用API保存订单
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    showToast({ type: 'success', message: '保存成功' })
    hasUnsavedChanges.value = false

    // 可选择是否返回列表页
    router.back()
  } catch (error) {
    showToast({ type: 'fail', message: '保存失败，请重试' })
  } finally {
    submitting.value = false
  }
}

/**
 * 保存并提交
 */
const handleSaveAndSubmit = async (): Promise<void> => {
  showValidation.value = true

  if (!validateForm()) {
    return
  }

  try {
    await showConfirmDialog({
      title: '确认提交',
      message: '提交后将无法修改，确定要提交吗？'
    })
  } catch {
    return // 用户取消
  }

  submitting.value = true

  try {
    // TODO: 调用API保存并提交订单
    await new Promise(resolve => setTimeout(resolve, 1500)) // 模拟API调用

    showToast({ type: 'success', message: '提交成功' })
    hasUnsavedChanges.value = false

    // 返回列表页
    router.back()
  } catch (error) {
    showToast({ type: 'fail', message: '提交失败，请重试' })
  } finally {
    submitting.value = false
  }
}

// 页面离开前确认
const beforeUnloadHandler = (event: BeforeUnloadEvent): void => {
  if (hasUnsavedChanges.value) {
    event.preventDefault()
    event.returnValue = '当前有未保存的内容，确定要离开吗？'
  }
}

// 初始化
onMounted(() => {
  initializeForm()

  // 监听页面刷新/关闭
  window.addEventListener('beforeunload', beforeUnloadHandler)
})

// 清理
onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', beforeUnloadHandler)
})
</script>

<style lang="less" scoped>
// 动画定义
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

.sales-order-add {
  min-height: 100vh;
  background: linear-gradient(135deg, #f7f8fa 0%, #e9ecef 100%);
  animation: fadeIn 0.3s ease-out;
}

.page-header {
  background: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  animation: slideInDown 0.4s ease-out;

  .nav-bar {
    :deep(.van-nav-bar__title) {
      font-size: 18px;
      font-weight: 600;
      color: #323233;
    }

    :deep(.van-nav-bar__left) {
      .van-icon {
        font-size: 20px;
        color: #323233;
        transition: all 0.2s ease;

        &:hover {
          color: #1989fa;
        }
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .help-icon {
      font-size: 20px;
      color: #1989fa;
      cursor: pointer;
      padding: 6px;
      border-radius: 50%;
      transition: all 0.3s ease;

      &:hover {
        background: #e6f7ff;
        transform: scale(1.1);
      }

      &:active {
        animation: buttonPress 0.2s ease;
      }
    }
  }

  .page-status {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

    .status-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #dcdee0;
        transition: all 0.3s ease;
      }

      .status-text {
        font-size: 12px;
        color: #969799;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      &.active {
        .status-dot {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          animation: pulse 2s infinite;
        }

        .status-text {
          color: #323233;
          font-weight: 600;
        }
      }
    }

    .status-line {
      width: 60px;
      height: 2px;
      background: #dcdee0;
      margin: 0 20px;
    }
  }
}

.form-container {
  animation: slideInUp 0.5s ease-out 0.2s both;

  .order-form {
    padding-bottom: 100px; // 为底部操作栏留空间
  }
}

.product-list {
  .product-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .product-info {
    flex: 1;
    
    .product-name {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .product-spec {
      font-size: 12px;
      color: #666;
    }
  }
  
  .product-quantity {
    margin: 0 12px;
  }
  
  .product-price {
    font-weight: 500;
    color: #ff6b35;
    margin-right: 12px;
  }
  
  .van-icon {
    color: #ee0a24;
    cursor: pointer;
  }
}

.amount-summary {
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
}

.attachment-info {
  padding: 12px 16px;
  
  .attachment-tip {
    font-size: 12px;
    color: #666;
    margin-bottom: 12px;
  }
  
  .attachment-note {
    font-size: 12px;
    color: #666;
    margin-top: 12px;
  }
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 16px 20px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  z-index: 100;
  animation: slideInUp 0.6s ease-out 0.4s both;

  .action-button {
    flex: 1;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    height: 48px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
      animation: buttonPress 0.2s ease;
    }

    &.primary-action {
      background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
      border: none;

      :deep(.van-icon) {
        margin-right: 6px;
        font-size: 18px;
      }

      &:hover {
        background: linear-gradient(135deg, #73d13d 0%, #52c41a 100%);
      }
    }

    &.secondary-action {
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      border: none;

      :deep(.van-icon) {
        margin-right: 6px;
        font-size: 18px;
      }

      &:hover {
        background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
      }
    }

    &:disabled {
      opacity: 0.6;
      transform: none;
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }
  }
}

.help-popup {
  border-radius: 12px;
  overflow: hidden;
  max-width: 320px;

  .help-content {
    padding: 24px;

    h3 {
      text-align: center;
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
      color: #323233;
    }

    .help-item {
      margin-bottom: 16px;

      h4 {
        font-size: 14px;
        font-weight: 600;
        color: #323233;
        margin-bottom: 8px;
      }

      p {
        font-size: 13px;
        color: #646566;
        line-height: 1.5;
        margin: 4px 0;
      }
    }

    .van-button {
      margin-top: 20px;
    }
  }
}

.help-popup {
  :deep(.van-popup) {
    border-radius: 16px;
    overflow: hidden;
  }

  .help-content {
    padding: 0;
    max-width: 360px;
    background: white;

    .help-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px 16px 24px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }

      .close-icon {
        font-size: 20px;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      }
    }

    .help-body {
      padding: 24px;

      .help-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .help-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          flex-shrink: 0;

          .van-icon {
            font-size: 18px;
            color: #1989fa;
          }
        }

        .help-text {
          flex: 1;

          h4 {
            margin: 0 0 8px 0;
            font-size: 15px;
            font-weight: 600;
            color: #323233;
          }

          p {
            margin: 0 0 4px 0;
            font-size: 14px;
            color: #646566;
            line-height: 20px;
          }
        }
      }
    }

    .help-footer {
      padding: 16px 24px 24px 24px;

      .van-button {
        height: 44px;
        font-size: 16px;
        font-weight: 600;
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        border: none;

        &:hover {
          background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
        }
      }
    }
  }
}

// 响应式设计优化
@media (max-width: @erp-mobile-xs) {
  .sales-order-add {
    // 小屏幕优化
    .page-header {
      padding: 12px 16px;

      .header-content {
        .page-title {
          font-size: 16px;
        }

        .help-icon {
          width: 32px;
          height: 32px;

          .van-icon {
            font-size: 16px;
          }
        }
      }
    }

    .progress-container {
      padding: 12px 16px;

      .progress-steps {
        .step {
          .step-number {
            width: 24px;
            height: 24px;
            font-size: 12px;
          }

          .step-label {
            font-size: 12px;
          }
        }

        .step-connector {
          height: 2px;
        }
      }
    }

    .form-container {
      margin: 8px;
      border-radius: 8px;
      padding: 16px;
    }

    .action-bar {
      padding: 12px 16px;

      .action-buttons {
        gap: 8px;

        .van-button {
          height: 40px;
          font-size: 14px;
          border-radius: 8px;

          &.save-button {
            min-width: 80px;
          }

          &.submit-button {
            min-width: 100px;
          }
        }
      }
    }
  }

  .help-popup {
    :deep(.van-popup) {
      border-radius: 12px 12px 0 0;
      max-height: 80vh;
    }

    .help-content {
      .help-header {
        padding: 16px 20px 12px 20px;

        h3 {
          font-size: 16px;
        }

        .close-icon {
          font-size: 18px;
        }
      }

      .help-body {
        padding: 20px;

        .help-item {
          margin-bottom: 16px;

          .help-icon {
            width: 36px;
            height: 36px;
            margin-right: 12px;

            .van-icon {
              font-size: 16px;
            }
          }

          .help-text {
            h4 {
              font-size: 14px;
            }

            p {
              font-size: 13px;
              line-height: 18px;
            }
          }
        }
      }

      .help-footer {
        padding: 12px 20px 20px 20px;

        .van-button {
          height: 40px;
          font-size: 14px;
        }
      }
    }
  }
}

@media (min-width: @erp-mobile-sm) and (max-width: @erp-mobile-lg) {
  .sales-order-add {
    // 中等屏幕优化
    .form-container {
      margin: 12px;
      padding: 20px;
    }

    .action-bar {
      padding: 16px 20px;

      .action-buttons {
        .van-button {
          height: 44px;
          font-size: 15px;
        }
      }
    }
  }
}

@media (min-width: @erp-tablet) {
  .sales-order-add {
    // 平板优化
    max-width: 600px;
    margin: 0 auto;

    .page-header {
      border-radius: 12px 12px 0 0;
      margin: 16px 16px 0 16px;
    }

    .progress-container {
      margin: 0 16px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .form-container {
      margin: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .action-bar {
      margin: 0 16px 16px 16px;
      border-radius: 0 0 12px 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .help-popup {
    :deep(.van-popup) {
      max-width: 500px;
      margin: 0 auto;
      border-radius: 16px;
    }
  }
}

// 触摸优化
@media (hover: none) and (pointer: coarse) {
  .sales-order-add {
    .page-header {
      .header-content {
        .help-icon {
          min-width: 44px;
          min-height: 44px;
        }
      }
    }

    .action-bar {
      .action-buttons {
        .van-button {
          min-height: 44px;
        }
      }
    }
  }
}

// 横屏优化
@media (orientation: landscape) and (max-height: 500px) {
  .sales-order-add {
    .progress-container {
      padding: 8px 16px;

      .progress-steps {
        .step {
          .step-number {
            width: 20px;
            height: 20px;
            font-size: 11px;
          }

          .step-label {
            font-size: 11px;
          }
        }
      }
    }

    .form-container {
      padding: 12px;
    }

    .action-bar {
      padding: 8px 16px;
    }
  }

  .help-popup {
    :deep(.van-popup) {
      max-height: 90vh;
    }
  }
}
</style>
