<!--
  ERP登录页面

  按照截图设计：蓝色渐变背景 + 企业标语 + 白色登录表单
-->
<template>
  <div class="login-page">
    <!-- 蓝色渐变背景头部 -->
    <div class="login-header">
      <h1 class="login-header__title">聆听东方之美</h1>
      <p class="login-header__subtitle">传承匠心工艺</p>
    </div>

    <!-- 登录表单容器 -->
    <div class="login-form-container">
      <div class="login-form">
        <van-form @submit="handleLogin">
          <van-field
            v-model="form.username"
            name="username"
            label="用户名"
            placeholder="请输入用户名"
            :rules="[{ required: true, message: '请输入用户名' }]"
            left-icon="contact"
            clearable
          />
          <van-field
            v-model="form.password"
            type="password"
            name="password"
            label="密码"
            placeholder="请输入密码"
            :rules="[{ required: true, message: '请输入密码' }]"
            left-icon="lock"
            clearable
          />

          <!-- 验证码输入框 -->
          <div class="captcha-container">
            <van-field
              v-model="form.captcha"
              name="captcha"
              label="验证码"
              placeholder="请输入验证码"
              :rules="[{ required: true, message: '请输入验证码' }]"
              left-icon="shield-o"
              clearable
              class="captcha-input"
            />
            <div class="captcha-image" @click="refreshCaptcha">
              <img
                v-if="captchaImage"
                :src="captchaImage"
                alt="验证码"
                :class="{ 'loading': captchaLoading }"
              />
              <div v-else class="captcha-placeholder">
                <van-loading v-if="captchaLoading" size="20px" />
                <span v-else>点击获取</span>
              </div>
            </div>
          </div>

          <div class="login-form__actions">
            <van-checkbox v-model="form.rememberMe">记住密码</van-checkbox>
          </div>

          <div class="login-form__submit">
            <van-button
              round
              block
              type="primary"
              native-type="submit"
              :loading="loading"
              size="large"
            >
              登录
            </van-button>
          </div>
        </van-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores'
import { authAdapter } from '@/api/adapters/auth'
import { showToast } from 'vant'

const router = useRouter()
const authStore = useAuthStore()
const loading = ref(false)
const captchaLoading = ref(false)
const captchaImage = ref('')
const captchaId = ref('')

const form = reactive({
  username: '',
  password: '',
  captcha: '',
  rememberMe: false
})

/**
 * 获取验证码
 */
const getCaptcha = async () => {
  captchaLoading.value = true
  try {
    const result = await authAdapter.getCaptcha()
    captchaImage.value = result.captchaImage
    captchaId.value = result.captchaId
  } catch (error: any) {
    showToast({ type: 'fail', message: error.message || '获取验证码失败' })
  } finally {
    captchaLoading.value = false
  }
}

/**
 * 刷新验证码
 */
const refreshCaptcha = () => {
  form.captcha = '' // 清空验证码输入
  getCaptcha()
}

/**
 * 处理登录
 */
const handleLogin = async () => {
  if (!form.captcha) {
    showToast({ type: 'fail', message: '请输入验证码' })
    return
  }

  loading.value = true
  try {
    await authStore.login({
      username: form.username,
      password: form.password,
      captcha: form.captcha,
      rememberMe: form.rememberMe
    })
    showToast({ type: 'success', message: '登录成功' })
    // 跳转到首页
    router.push('/home')
  } catch (error: any) {
    showToast({ type: 'fail', message: error.message || '登录失败' })
    console.error('Login error:', error)
    // 登录失败时刷新验证码
    refreshCaptcha()
  } finally {
    loading.value = false
  }
}

/**
 * 页面初始化
 */
onMounted(() => {
  getCaptcha()
})
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.login-page {
  min-height: 100vh;
  background: var(--erp-gradient);
  display: flex;
  flex-direction: column;
  position: relative;
}

.login-header {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--erp-spacing-xl) var(--erp-spacing-md);
  text-align: center;

  &__title {
    font-size: var(--erp-font-size-3xl);
    font-weight: var(--erp-font-weight-bold);
    color: var(--erp-text-white);
    margin: 0 0 var(--erp-spacing-md) 0;
    line-height: var(--erp-line-height-tight);
  }

  &__subtitle {
    font-size: var(--erp-font-size-lg);
    color: var(--erp-text-white);
    opacity: 0.9;
    margin: 0;
    line-height: var(--erp-line-height-normal);
  }
}

.login-form-container {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: var(--erp-spacing-xl) var(--erp-spacing-md) var(--erp-spacing-2xl);
}

.login-form {
  width: 100%;
  max-width: 400px;
  background: var(--erp-bg-card);
  border-radius: var(--erp-radius-xl);
  box-shadow: var(--erp-shadow-heavy);
  padding: var(--erp-spacing-xl);

  :deep(.van-field) {
    margin-bottom: var(--erp-spacing-md);

    .van-field__label {
      color: var(--erp-text-primary);
      font-weight: var(--erp-font-weight-medium);
    }

    .van-field__control {
      font-size: var(--erp-font-size-md);
    }
  }

  // 验证码容器样式
  .captcha-container {
    display: flex;
    align-items: flex-end;
    gap: var(--erp-spacing-sm);
    margin-bottom: var(--erp-spacing-md);

    .captcha-input {
      flex: 1;
      margin-bottom: 0;
    }

    .captcha-image {
      width: 100px;
      height: 40px;
      border: 1px solid var(--erp-border-color);
      border-radius: var(--erp-radius-sm);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--erp-bg-secondary);
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--erp-primary);
        background: var(--erp-bg-card);
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: calc(var(--erp-radius-sm) - 1px);
        transition: opacity 0.3s ease;

        &.loading {
          opacity: 0.6;
        }
      }

      .captcha-placeholder {
        color: var(--erp-text-secondary);
        font-size: var(--erp-font-size-sm);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
      }
    }
  }

  &__actions {
    padding: var(--erp-spacing-md) 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    :deep(.van-checkbox) {
      .van-checkbox__label {
        color: var(--erp-text-secondary);
        font-size: var(--erp-font-size-sm);
      }
    }
  }

  &__submit {
    margin-top: var(--erp-spacing-lg);

    :deep(.van-button) {
      height: 48px;
      font-size: var(--erp-font-size-lg);
      font-weight: var(--erp-font-weight-semibold);
      background: var(--erp-primary);
      border-color: var(--erp-primary);

      &:active {
        background: var(--erp-primary-dark);
        border-color: var(--erp-primary-dark);
      }
    }
  }
}

// 响应式适配
@media (max-width: @erp-mobile-xs) {
  .login-header {
    &__title {
      font-size: var(--erp-font-size-2xl);
    }

    &__subtitle {
      font-size: var(--erp-font-size-md);
    }
  }

  .login-form-container {
    padding: var(--erp-spacing-lg) var(--erp-spacing-md) var(--erp-spacing-xl);
  }

  .login-form {
    padding: var(--erp-spacing-lg);
  }
}
</style>