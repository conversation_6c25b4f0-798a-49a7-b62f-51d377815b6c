<template>
  <div class="dashboard">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <!-- 数据概览卡片 -->
      <div class="dashboard__cards">
        <van-grid :column-num="2" :gutter="16">
          <van-grid-item>
            <div class="stat-card">
              <div class="stat-card__value">¥{{ formatMoney(dashboardData?.todaySales || 0) }}</div>
              <div class="stat-card__label">今日销售额</div>
            </div>
          </van-grid-item>
          <van-grid-item>
            <div class="stat-card">
              <div class="stat-card__value">{{ dashboardData?.todayOrders || 0 }}</div>
              <div class="stat-card__label">今日订单</div>
            </div>
          </van-grid-item>
          <van-grid-item>
            <div class="stat-card">
              <div class="stat-card__value">{{ dashboardData?.lowStockCount || 0 }}</div>
              <div class="stat-card__label">库存预警</div>
            </div>
          </van-grid-item>
          <van-grid-item>
            <div class="stat-card">
              <div class="stat-card__value">{{ dashboardData?.pendingTasks || 0 }}</div>
              <div class="stat-card__label">待处理</div>
            </div>
          </van-grid-item>
        </van-grid>
      </div>
      
      <!-- 快速操作 -->
      <van-cell-group title="快速操作" inset>
        <van-cell title="新建订单" is-link @click="goToSales" />
        <van-cell title="库存盘点" is-link @click="goToInventory" />
        <van-cell title="财务报表" is-link />
        <van-cell title="系统设置" is-link />
      </van-cell-group>
    </van-pull-refresh>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBusinessStore } from '@/stores'
import { utils } from '@/utils'

const router = useRouter()
const businessStore = useBusinessStore()
const refreshing = ref(false)

const { dashboardData } = storeToRefs(businessStore)

const formatMoney = (amount: number) => {
  return utils.formatMoney(amount)
}

const onRefresh = async () => {
  try {
    await businessStore.fetchDashboardData()
  } finally {
    refreshing.value = false
  }
}

const goToSales = () => {
  router.push('/business/sales')
}

const goToInventory = () => {
  router.push('/business/inventory')
}

onMounted(() => {
  businessStore.fetchDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: var(--spacing-md);
}

.dashboard__cards {
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  text-align: center;
  padding: var(--spacing-lg);
  background: white;
  border-radius: var(--radius-md);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-card__value {
  font-size: 24px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.stat-card__label {
  font-size: 14px;
  color: var(--text-secondary);
}
</style>