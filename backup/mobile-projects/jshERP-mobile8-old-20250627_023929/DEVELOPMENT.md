# jshERP移动端开发指南

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 9.0.0
- Git >= 2.30.0

### 安装和启动
```bash
# 克隆项目
git clone <repository-url>
cd jshERP-mobile

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问应用
open http://localhost:8083
```

## 📁 项目结构

```
src/
├── api/                    # API接口层
│   ├── adapters/          # 数据适配器
│   ├── endpoints/         # 接口端点
│   └── types/             # API类型定义
├── components/            # 组件库
│   ├── base/             # 基础组件
│   ├── business/         # 业务组件
│   └── layout/           # 布局组件
├── composables/          # 组合式函数
├── layouts/              # 页面布局
├── plugins/              # 插件配置
├── router/               # 路由配置
├── stores/               # Pinia状态管理
├── styles/               # 样式文件
├── utils/                # 工具函数
└── views/                # 页面组件
```

## 🛠️ 开发规范

### 代码规范

#### TypeScript
- 所有文件使用TypeScript编写
- 严格的类型检查，不允许any类型（除非必要）
- 使用接口定义数据结构
- 导入类型时使用`type`关键字

```typescript
// ✅ 正确
import { type UserInfo } from '@/types'

// ❌ 错误
import { UserInfo } from '@/types'
```

#### Vue组件
- 使用Composition API
- 组件名使用PascalCase
- Props和Events要有完整的类型定义
- 使用`<script setup>`语法

```vue
<script setup lang="ts">
interface Props {
  /** 用户信息 */
  userInfo: UserInfo
  /** 是否显示详情 */
  showDetail?: boolean
}

interface Emits {
  /** 点击事件 */
  click: [user: UserInfo]
  /** 更新事件 */
  update: [data: Partial<UserInfo>]
}

const props = withDefaults(defineProps<Props>(), {
  showDetail: false
})

const emit = defineEmits<Emits>()
</script>
```

#### 样式规范
- 使用CSS变量定义主题色彩
- 组件样式使用scoped
- 遵循BEM命名规范
- 使用Less预处理器

```vue
<style scoped lang="less">
.user-card {
  padding: var(--spacing-md);
  background: var(--white);
  border-radius: var(--radius-md);
  
  &__avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
  }
  
  &__info {
    margin-left: var(--spacing-md);
  }
  
  &--active {
    border: 2px solid var(--primary-color);
  }
}
</style>
```

### 命名规范

#### 文件命名
- 组件文件：PascalCase（如：`UserCard.vue`）
- 工具文件：camelCase（如：`dateUtils.ts`）
- 页面文件：PascalCase（如：`UserProfile.vue`）
- 类型文件：camelCase（如：`userTypes.ts`）

#### 变量命名
- 变量和函数：camelCase
- 常量：UPPER_SNAKE_CASE
- 类名：PascalCase
- 接口：PascalCase，以I开头（可选）

```typescript
// 变量和函数
const userName = 'john'
const getUserInfo = () => {}

// 常量
const API_BASE_URL = 'https://api.example.com'

// 类
class UserService {}

// 接口
interface UserInfo {}
// 或
interface IUserInfo {}
```

## 🔧 开发工具

### VSCode配置
推荐安装以下扩展：
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- Auto Rename Tag
- Bracket Pair Colorizer

### Git工作流
```bash
# 创建功能分支
git checkout -b feature/user-profile

# 提交代码（会自动运行lint检查）
git add .
git commit -m "feat: add user profile component"

# 推送分支
git push origin feature/user-profile
```

### 调试技巧
1. **Vue DevTools**：安装浏览器扩展进行组件调试
2. **Network面板**：查看API请求和响应
3. **Console**：使用console.log进行调试（生产环境会自动移除）
4. **Breakpoints**：在浏览器中设置断点调试

## 📦 构建和部署

### 本地构建
```bash
# 类型检查
npm run type-check

# 代码检查
npm run lint

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 环境配置
- 开发环境：`.env.development`
- 生产环境：`.env.production`

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:9999
VITE_APP_TITLE=jshERP移动端（开发）

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=jshERP移动端
```

## 🧪 测试

### 单元测试
```bash
# 运行测试
npm run test

# 测试覆盖率
npm run test:coverage
```

### E2E测试
```bash
# 运行E2E测试
npm run test:e2e
```

## 📱 移动端开发注意事项

### 响应式设计
- 使用相对单位（rem、em、%）
- 设置合适的viewport
- 考虑不同屏幕密度

### 性能优化
- 图片懒加载
- 路由懒加载
- 组件按需引入
- 避免内存泄漏

### 用户体验
- 触摸友好的交互
- 合适的点击区域（最小44px）
- 加载状态提示
- 错误处理和重试机制

## 🔍 常见问题

### Q: TypeScript报错怎么办？
A: 运行`npm run type-check`查看具体错误，确保所有类型定义正确。

### Q: ESLint警告如何处理？
A: 运行`npm run lint`查看警告，大部分可以通过`npm run lint -- --fix`自动修复。

### Q: 如何添加新的API？
A: 在`src/api/adapters/`中创建适配器，在`src/api/types/`中定义类型。

### Q: 如何添加新页面？
A: 在`src/views/`中创建页面组件，在`src/router/`中添加路由配置。

## 📚 参考资料

- [Vue 3 官方文档](https://vuejs.org/)
- [Vant 4 组件库](https://vant-contrib.gitee.io/vant/)
- [Pinia 状态管理](https://pinia.vuejs.org/)
- [TypeScript 手册](https://www.typescriptlang.org/docs/)
- [ESLint 规则](https://eslint.org/docs/rules/)
