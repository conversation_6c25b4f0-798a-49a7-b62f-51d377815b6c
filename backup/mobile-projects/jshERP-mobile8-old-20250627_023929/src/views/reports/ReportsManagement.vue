<!--
  报表管理页面

  包含报表查询和高级报表两个功能分组
-->
<template>
  <div class="erp-page">
    <!-- 页面头部 -->
    <div class="erp-header erp-header--simple">
      <h1 class="erp-header__title">报表</h1>
    </div>

    <!-- 功能分组 -->
    <div v-for="section in sections" :key="section.title" class="erp-section">
      <ERPSectionTitle :title="section.title" />
      <div class="erp-section__content">
        <ERPFunctionGrid
          :items="section.items"
          :columns="4"
          @item-click="handleFunctionClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant'
import ERPSectionTitle from '@/components/erp/ERPSectionTitle.vue'
import ERPFunctionGrid from '@/components/erp/ERPFunctionGrid.vue'
import type { FunctionItem, FunctionSection } from '@/types/erp'

/**
 * 报表管理功能配置
 */
const sections: FunctionSection[] = [
  {
    title: '报表查询',
    items: [
      { id: 'goods-inventory', icon: 'goods-collect-o', label: '商品库存', color: '#FF6B35' },
      { id: 'account-stats', icon: 'chart-trending-o', label: '账户统计', color: '#FF6B35' },
      { id: 'retail-stats', icon: 'shop-o', label: '零售统计', color: '#FF6B35' },
      { id: 'purchase-stats', icon: 'shopping-cart-o', label: '采购统计', color: '#FF6B35' },
      { id: 'sales-stats', icon: 'gold-coin-o', label: '销售统计', color: '#FF6B35' },
      { id: 'inbound-detail', icon: 'logistics', label: '入库明细', color: '#FF6B35' },
      { id: 'outbound-detail', icon: 'send-gift-o', label: '出库明细', color: '#FF6B35' },
      { id: 'transfer-detail', icon: 'exchange', label: '调拨明细', color: '#FF6B35' },
      { id: 'inbound-summary', icon: 'logistics', label: '入库汇总', color: '#FF6B35' },
      { id: 'outbound-summary', icon: 'send-gift-o', label: '出库汇总', color: '#FF6B35' },
      { id: 'inventory-stats', icon: 'bar-chart-o', label: '进销存统计', color: '#FF6B35' },
      { id: 'customer-reconcile', icon: 'contact', label: '客户对账', color: '#FF6B35' },
      { id: 'supplier-reconcile', icon: 'shop-o', label: '供应商对账', color: '#FF6B35' },
      { id: 'inventory-alert', icon: 'warning-o', label: '库存预警', color: '#FF6B35' }
    ]
  },
  {
    title: '高级报表',
    items: [
      { id: 'income-detail', icon: 'gold-coin-o', label: '收支项明细', color: '#FF6B35' },
      { id: 'payment-detail', icon: 'credit-pay', label: '收付款明细', color: '#FF6B35' },
      { id: 'sales-ratio', icon: 'chart-trending-o', label: '销售占比', color: '#FF6B35' },
      { id: 'sales-ranking', icon: 'medal-o', label: '销售排行', color: '#FF6B35' }
    ]
  }
]

/**
 * 处理功能项点击
 */
const handleFunctionClick = (item: FunctionItem): void => {
  console.log('Report function clicked:', item)
  showToast(`点击了${item.label}`)

  // TODO: 根据报表类型跳转到对应页面或生成报表
  switch (item.id) {
    case 'goods-inventory':
      // 跳转到商品库存报表
      break
    case 'sales-stats':
      // 跳转到销售统计报表
      break
    case 'sales-ranking':
      // 生成销售排行报表
      break
    default:
      // 其他报表的处理逻辑
      break
  }
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';
@import '@/styles/common.less';

// 页面特定样式
.erp-section {
  &__content {
    background: var(--erp-bg-card);
    border-radius: var(--erp-radius-lg);
    box-shadow: var(--erp-shadow-card);
    margin: 0 var(--erp-spacing-md) var(--erp-spacing-lg);
    overflow: hidden;
  }
}
</style>
