<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; white-space: pre-wrap; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        img { max-width: 200px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>jshERP API 测试</h1>
    
    <div class="test-section">
        <h3>1. 验证码测试</h3>
        <button onclick="testCaptcha()">获取验证码</button>
        <div id="captcha-result" class="result"></div>
        <div id="captcha-image"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 登录测试</h3>
        <input type="text" id="username" placeholder="用户名" value="jsh">
        <input type="password" id="password" placeholder="密码" value="123456">
        <input type="text" id="captcha-input" placeholder="验证码">
        <button onclick="testLogin()">登录</button>
        <div id="login-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:9999/jshERP-boot';
        let captchaUuid = '';

        async function testCaptcha() {
            try {
                const response = await fetch(`${API_BASE}/user/randomImage`);
                const data = await response.json();
                
                document.getElementById('captcha-result').innerHTML = 
                    `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                document.getElementById('captcha-result').className = 'result success';
                
                if (data.data && data.data.base64) {
                    captchaUuid = data.data.uuid;
                    document.getElementById('captcha-image').innerHTML = 
                        `<img src="${data.data.base64}" alt="验证码">`;
                }
            } catch (error) {
                document.getElementById('captcha-result').innerHTML = `错误: ${error.message}`;
                document.getElementById('captcha-result').className = 'result error';
            }
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const captcha = document.getElementById('captcha-input').value;
            
            if (!captcha) {
                alert('请先获取验证码并输入');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/user/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        loginName: username,  // jshERP使用loginName
                        password: password,
                        code: captcha,        // jshERP使用code
                        uuid: captchaUuid
                    })
                });
                
                const data = await response.json();
                
                document.getElementById('login-result').innerHTML = 
                    `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                document.getElementById('login-result').className = 
                    data.code === 200 ? 'result success' : 'result error';
                    
            } catch (error) {
                document.getElementById('login-result').innerHTML = `错误: ${error.message}`;
                document.getElementById('login-result').className = 'result error';
            }
        }

        // 页面加载时自动获取验证码
        window.onload = function() {
            testCaptcha();
        };
    </script>
</body>
</html>
