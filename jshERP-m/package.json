{"name": "jsh-erp-mobile", "version": "3.5.0", "private": true, "scripts": {"serve": "vue-cli-service serve --port 8899", "dev": "vue-cli-service serve --port 8899", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode development", "build:test": "vue-cli-service build --mode test", "build:prod": "vue-cli-service build --mode production", "build:analyze": "cross-env ANALYZE=true vue-cli-service build", "mobile:dev": "vue-cli-service serve --port 8899 --mode mobile", "mobile:build": "vue-cli-service build --mode mobile", "preview": "node scripts/preview.js", "lint": "vue-cli-service lint", "lint:fix": "vue-cli-service lint --fix", "test:unit": "vue-cli-service test:unit", "docker:build": "docker build -t jsh-erp-mobile .", "docker:run": "docker run -p 8899:80 jsh-erp-mobile", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:prod": "docker-compose up -d", "deploy:dev": "./scripts/deploy.sh dev --build", "deploy:test": "./scripts/deploy.sh test --build", "deploy:prod": "./scripts/deploy.sh prod --build", "monitor": "./scripts/monitor.sh --check", "logs": "./scripts/monitor.sh --logs", "stats": "./scripts/monitor.sh --stats", "sync": "node scripts/sync-manager.js sync", "sync:full": "node scripts/sync-manager.js sync full", "sync:start": "node scripts/sync-manager.js start", "sync:status": "node scripts/sync-manager.js status", "sync:history": "node scripts/sync-manager.js history"}, "dependencies": {"@antv/data-set": "^0.11.2", "@tinymce/tinymce-vue": "^2.0.0", "ant-design-vue": "1.5.2", "area-data": "^5.0.6", "axios": "^0.18.0", "clipboard": "^2.0.4", "codemirror": "^5.46.0", "dayjs": "^1.8.0", "echarts": "^5.6.0", "enquire.js": "^2.1.6", "intro.js": "^4.2.2", "jquery": "^1.12.4", "js-cookie": "^2.2.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.2.1", "nprogress": "^0.2.0", "viser-vue": "^2.4.4", "vue": "^2.7.16", "vue-area-linkage": "^5.1.0", "vue-cropper": "^0.4.8", "vue-draggable-resizable": "^2.3.0", "vue-i18n": "^8.7.0", "vue-loader": "^15.7.0", "vue-ls": "^3.2.0", "vue-photo-preview": "^1.1.3", "vue-print-nb-jeecg": "^1.0.9", "vue-router": "^3.0.1", "vue-splitpane": "^1.0.4", "vuedraggable": "^2.20.0", "vuex": "^3.1.0"}, "devDependencies": {"@babel/polyfill": "^7.2.5", "@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-plugin-pwa": "^4.5.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "babel-plugin-import": "^1.13.3", "chokidar": "^4.0.3", "compression-webpack-plugin": "^3.1.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.1.0", "html-webpack-plugin": "^4.2.0", "image-webpack-loader": "^8.1.0", "less": "^3.9.0", "less-loader": "^4.1.0", "postcss-px-to-viewport": "^1.1.1", "sass": "^1.89.2", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/strongly-recommended", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"generator-star-spacing": "off", "no-mixed-operators": 0, "vue/max-attributes-per-line": [2, {"singleline": 5, "multiline": {"max": 1, "allowFirstLine": false}}], "vue/attribute-hyphenation": 0, "vue/html-self-closing": 0, "vue/component-name-in-template-casing": 0, "vue/html-closing-bracket-spacing": 0, "vue/singleline-html-element-content-newline": 0, "vue/no-unused-components": 0, "vue/multiline-html-element-content-newline": 0, "vue/no-use-v-if-with-v-for": 0, "vue/html-closing-bracket-newline": 0, "vue/no-parsing-error": 0, "no-console": 0, "no-tabs": 0, "indent": [1, 4]}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}