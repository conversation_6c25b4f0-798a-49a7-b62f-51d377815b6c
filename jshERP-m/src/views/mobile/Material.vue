<template>
  <div class="mobile-material">
    <!-- 顶部搜索栏 -->
    <div class="material-header">
      <div class="search-section">
        <a-input-search
          v-model="searchValue"
          placeholder="搜索商品名称、编号、条码"
          @search="handleSearch"
          @change="handleSearchChange"
          allowClear
        />
      </div>
      
      <!-- 筛选和操作按钮 -->
      <div class="action-section">
        <a-button type="text" @click="showFilterDrawer = true">
          <a-icon type="filter" />
          筛选
        </a-button>
        <a-button type="text" @click="showCategoryDrawer = true">
          <a-icon type="appstore" />
          分类
        </a-button>
        <a-button type="primary" @click="handleAdd">
          <a-icon type="plus" />
          添加
        </a-button>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="material-content">
      <mobile-list
        :dataSource="materialList"
        :loading="loading"
        :showHeader="false"
        :showSearch="false"
        :showActions="true"
        :showAvatar="true"
        :selectable="selectionMode"
        :selectedItems="selectedMaterials"
        :pagination="pagination"
        :itemActions="itemActions"
        titleField="name"
        descriptionField="description"
        timeField="updateTimeText"
        tagsField="tags"
        extraField="stockInfo"
        @itemClick="handleItemClick"
        @selectionChange="handleSelectionChange"
        @actionClick="handleActionClick"
        @pageChange="handlePageChange"
        @refresh="loadMaterialList"
      >
        <!-- 自定义商品头像 -->
        <template slot="avatar" slot-scope="{ item }">
          <a-avatar
            :size="48"
            :src="item.imgUrl"
            :style="{ backgroundColor: item.imgUrl ? 'transparent' : '#f56a00' }"
          >
            {{ item.imgUrl ? '' : (item.name ? item.name.charAt(0) : '商') }}
          </a-avatar>
        </template>

        <!-- 自定义商品项内容 -->
        <template slot="item" slot-scope="{ item }">
          <div class="material-item-content">
            <!-- 标题行 -->
            <div class="material-item-title">
              <span class="material-name">{{ item.name }}</span>
              <span class="material-price">¥{{ formatPrice(item.retailPrice) }}</span>
            </div>
            
            <!-- 商品信息行 -->
            <div class="material-item-info">
              <span class="material-code">编号: {{ item.materialNumber || '无' }}</span>
              <span class="material-unit">单位: {{ item.unitName || '个' }}</span>
            </div>
            
            <!-- 库存信息行 -->
            <div class="material-item-stock">
              <span class="stock-current" :class="getStockClass(item)">
                库存: {{ item.currentStock || 0 }}
              </span>
              <span class="stock-warning" v-if="item.lowSafeStock">
                预警: {{ item.lowSafeStock }}
              </span>
            </div>
            
            <!-- 标签行 -->
            <div class="material-item-tags" v-if="item.tags && item.tags.length">
              <a-tag
                v-for="tag in item.tags"
                :key="tag.key"
                :color="tag.color"
                size="small"
              >
                {{ tag.label }}
              </a-tag>
            </div>
          </div>
        </template>
      </mobile-list>
    </div>

    <!-- 底部批量操作栏 -->
    <div v-if="selectionMode && selectedMaterials.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedMaterials.length }} 项
      </div>
      <div class="batch-buttons">
        <a-button @click="batchEdit">批量编辑</a-button>
        <a-button @click="batchSetStock">修正库存</a-button>
        <a-button @click="batchSetPrice">修正价格</a-button>
        <a-button type="danger" @click="batchDelete">删除</a-button>
      </div>
    </div>

    <!-- 筛选抽屉 -->
    <mobile-drawer
      v-model="showFilterDrawer"
      title="筛选条件"
      placement="bottom"
      :showFooter="true"
      @confirm="applyFilter"
      @cancel="resetFilter"
    >
      <mobile-form
        :fields="filterFields"
        :model="filterModel"
        @change="handleFilterChange"
      />
    </mobile-drawer>

    <!-- 分类选择抽屉 -->
    <mobile-drawer
      v-model="showCategoryDrawer"
      title="选择分类"
      placement="right"
      :width="280"
    >
      <div class="category-tree">
        <a-tree
          :treeData="categoryTree"
          :selectedKeys="selectedCategoryKeys"
          @select="handleCategorySelect"
          :showIcon="false"
        />
      </div>
    </mobile-drawer>

    <!-- 商品详情/编辑模态框 -->
    <mobile-modal
      v-model="showMaterialModal"
      :title="materialModalTitle"
      :fullscreen="$isMobile"
      :showFooter="materialModalMode !== 'view'"
      @ok="handleMaterialSave"
      @cancel="handleMaterialCancel"
    >
      <material-form
        ref="materialForm"
        :mode="materialModalMode"
        :materialData="currentMaterial"
        @change="handleMaterialFormChange"
      />
    </mobile-modal>

    <!-- 批量操作模态框 -->
    <mobile-modal
      v-model="showBatchModal"
      :title="batchModalTitle"
      @ok="handleBatchSave"
      @cancel="showBatchModal = false"
    >
      <batch-operation-form
        ref="batchForm"
        :type="batchType"
        :materials="selectedMaterials"
        @change="handleBatchFormChange"
      />
    </mobile-modal>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { MobileList, MobileDrawer, MobileModal, MobileForm } from '@/components/mobile'
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
import MaterialForm from './components/MaterialForm'
import BatchOperationForm from './components/BatchOperationForm'

export default {
  name: 'MobileMaterial',
  mixins: [ResponsiveMixin],
  components: {
    MobileList,
    MobileDrawer,
    MobileModal,
    MobileForm,
    MaterialForm,
    BatchOperationForm
  },

  data() {
    return {
      // 搜索和筛选
      searchValue: '',
      showFilterDrawer: false,
      showCategoryDrawer: false,
      filterModel: {},
      selectedCategoryKeys: [],
      
      // 列表数据
      materialList: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: false,
        showQuickJumper: false
      },
      
      // 选择模式
      selectionMode: false,
      selectedMaterials: [],
      
      // 模态框
      showMaterialModal: false,
      materialModalMode: 'add', // add, edit, view
      materialModalTitle: '',
      currentMaterial: null,
      
      // 批量操作
      showBatchModal: false,
      batchModalTitle: '',
      batchType: '',
      
      // 分类树
      categoryTree: [],
      
      // 筛选字段配置
      filterFields: [
        {
          key: 'categoryId',
          type: 'select',
          label: '商品分类',
          options: [],
          placeholder: '请选择分类'
        },
        {
          key: 'enabled',
          type: 'radio',
          label: '状态',
          options: [
            { value: '', label: '全部' },
            { value: '1', label: '启用' },
            { value: '0', label: '禁用' }
          ]
        },
        {
          key: 'stockStatus',
          type: 'radio',
          label: '库存状态',
          options: [
            { value: '', label: '全部' },
            { value: 'normal', label: '正常' },
            { value: 'low', label: '库存不足' },
            { value: 'zero', label: '零库存' }
          ]
        }
      ],
      
      // 操作菜单
      itemActions: [
        { key: 'view', label: '查看详情', icon: 'eye' },
        { key: 'edit', label: '编辑', icon: 'edit' },
        { key: 'copy', label: '复制', icon: 'copy' },
        { key: 'delete', label: '删除', icon: 'delete', disabled: item => item.hasStock }
      ]
    }
  },

  computed: {
    materialModalTitle() {
      const titleMap = {
        add: '添加商品',
        edit: '编辑商品',
        view: '商品详情'
      }
      return titleMap[this.materialModalMode] || '商品信息'
    },

    batchModalTitle() {
      const titleMap = {
        edit: '批量编辑',
        stock: '批量修正库存',
        price: '批量修正价格'
      }
      return titleMap[this.batchType] || '批量操作'
    }
  },

  created() {
    this.loadMaterialList()
    this.loadCategoryTree()
    this.initFilterOptions()
  },

  methods: {
    // 加载商品列表
    async loadMaterialList() {
      this.loading = true
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.pageSize,
          search: this.searchValue,
          ...this.filterModel
        }
        
        const res = await getAction('/material/list', params)
        if (res.success) {
          this.materialList = res.result.records.map(item => ({
            ...item,
            updateTimeText: this.formatTime(item.updateTime),
            tags: this.getMaterialTags(item),
            stockInfo: this.getStockInfo(item)
          }))
          
          this.pagination.total = res.result.total
        }
      } catch (error) {
        console.error('加载商品列表失败:', error)
        this.$message.error('加载商品列表失败')
      } finally {
        this.loading = false
      }
    },

    // 加载分类树
    async loadCategoryTree() {
      try {
        const res = await getAction('/materialCategory/tree')
        if (res.success) {
          this.categoryTree = res.result
        }
      } catch (error) {
        console.error('加载分类树失败:', error)
      }
    },

    // 初始化筛选选项
    async initFilterOptions() {
      try {
        const res = await getAction('/materialCategory/list')
        if (res.success) {
          const categoryField = this.filterFields.find(f => f.key === 'categoryId')
          if (categoryField) {
            categoryField.options = res.result.map(item => ({
              value: item.id,
              label: item.name
            }))
          }
        }
      } catch (error) {
        console.error('初始化筛选选项失败:', error)
      }
    },

    // 处理搜索
    handleSearch(value) {
      this.searchValue = value
      this.pagination.current = 1
      this.loadMaterialList()
    },

    // 处理搜索变化
    handleSearchChange(e) {
      if (!e.target.value) {
        this.handleSearch('')
      }
    },

    // 处理分页变化
    handlePageChange(page, pageSize) {
      this.pagination.current = page
      this.pagination.pageSize = pageSize
      this.loadMaterialList()
    },

    // 处理商品项点击
    handleItemClick(item) {
      this.currentMaterial = item
      this.materialModalMode = 'view'
      this.showMaterialModal = true
    },

    // 处理操作点击
    handleActionClick(actionKey, item) {
      switch (actionKey) {
        case 'view':
          this.handleItemClick(item)
          break
        case 'edit':
          this.handleEdit(item)
          break
        case 'copy':
          this.handleCopy(item)
          break
        case 'delete':
          this.handleDelete(item)
          break
      }
    },

    // 处理选择变化
    handleSelectionChange(selectedItems) {
      this.selectedMaterials = selectedItems
    },

    // 添加商品
    handleAdd() {
      this.currentMaterial = null
      this.materialModalMode = 'add'
      this.showMaterialModal = true
    },

    // 编辑商品
    handleEdit(item) {
      this.currentMaterial = item
      this.materialModalMode = 'edit'
      this.showMaterialModal = true
    },

    // 复制商品
    handleCopy(item) {
      this.currentMaterial = { ...item, id: null, materialNumber: null }
      this.materialModalMode = 'add'
      this.showMaterialModal = true
    },

    // 删除商品
    handleDelete(item) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除商品"${item.name}"吗？`,
        onOk: async () => {
          try {
            const res = await deleteAction('/material/delete', { id: item.id })
            if (res.success) {
              this.$message.success('删除成功')
              this.loadMaterialList()
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        }
      })
    },

    // 获取商品标签
    getMaterialTags(item) {
      const tags = []
      
      if (item.enabled === '0') {
        tags.push({ key: 'disabled', label: '已禁用', color: 'red' })
      }
      
      if (item.currentStock <= item.lowSafeStock) {
        tags.push({ key: 'lowStock', label: '库存不足', color: 'orange' })
      }
      
      if (item.isSerialNumber === '1') {
        tags.push({ key: 'serial', label: '序列号', color: 'blue' })
      }
      
      return tags
    },

    // 获取库存信息
    getStockInfo(item) {
      return `成本: ¥${item.purchaseDecimalPrice || 0}`
    },

    // 获取库存样式类
    getStockClass(item) {
      if (item.currentStock <= 0) return 'stock-zero'
      if (item.currentStock <= item.lowSafeStock) return 'stock-low'
      return 'stock-normal'
    },

    // 格式化价格
    formatPrice(price) {
      return Number(price || 0).toFixed(2)
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleDateString()
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/styles/mobile/index.less';

.mobile-material {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: @background-color-light;
  
  .material-header {
    background-color: white;
    padding: @spacing-md @spacing-lg;
    border-bottom: 1px solid @border-color-light;
    
    .search-section {
      margin-bottom: @spacing-md;
    }
    
    .action-section {
      .d-flex();
      justify-content: space-between;
      align-items: center;
      
      .ant-btn {
        .flex-center();
        gap: @spacing-xs;
      }
    }
  }
  
  .material-content {
    flex: 1;
    overflow: hidden;
    
    .material-item-content {
      width: 100%;
      
      .material-item-title {
        .flex-between();
        align-items: flex-start;
        margin-bottom: @spacing-xs;
        
        .material-name {
          font-size: @font-size-base;
          font-weight: @font-weight-medium;
          color: @text-color;
          flex: 1;
          .text-ellipsis();
        }
        
        .material-price {
          font-size: @font-size-lg;
          font-weight: @font-weight-semibold;
          color: @primary-color;
          margin-left: @spacing-sm;
        }
      }
      
      .material-item-info {
        .d-flex();
        gap: @spacing-lg;
        margin-bottom: @spacing-xs;
        
        .material-code,
        .material-unit {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
      
      .material-item-stock {
        .d-flex();
        gap: @spacing-lg;
        margin-bottom: @spacing-xs;
        
        .stock-current {
          font-size: @font-size-sm;
          font-weight: @font-weight-medium;
          
          &.stock-normal {
            color: @success-color;
          }
          
          &.stock-low {
            color: @warning-color;
          }
          
          &.stock-zero {
            color: @error-color;
          }
        }
        
        .stock-warning {
          font-size: @font-size-sm;
          color: @text-color-tertiary;
        }
      }
      
      .material-item-tags {
        .ant-tag {
          margin-right: @spacing-xs;
          margin-bottom: @spacing-xs;
        }
      }
    }
  }
  
  .batch-actions {
    background-color: white;
    padding: @spacing-md @spacing-lg;
    border-top: 1px solid @border-color-light;
    .flex-between();
    align-items: center;
    
    .batch-info {
      font-size: @font-size-sm;
      color: @text-color-secondary;
    }
    
    .batch-buttons {
      .d-flex();
      gap: @spacing-sm;
      
      .ant-btn {
        font-size: @font-size-sm;
        padding: @spacing-xs @spacing-sm;
      }
    }
  }
  
  .category-tree {
    padding: @spacing-md;
    
    .ant-tree {
      .ant-tree-node-content-wrapper {
        padding: @spacing-sm;
        border-radius: @border-radius-sm;
        
        &:hover {
          background-color: @background-color-light;
        }
        
        &.ant-tree-node-selected {
          background-color: @primary-color-light;
          color: @primary-color;
        }
      }
    }
  }
}
</style>
