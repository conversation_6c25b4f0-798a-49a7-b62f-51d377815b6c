<template>
  <div class="mobile-orders">
    <!-- 顶部标签切换 -->
    <div class="orders-header">
      <a-tabs v-model="activeOrderType" @change="handleOrderTypeChange">
        <a-tab-pane key="sale" tab="销售订单" />
        <a-tab-pane key="purchase" tab="采购订单" />
      </a-tabs>
      
      <!-- 搜索栏 -->
      <div class="search-section">
        <a-input-search
          v-model="searchValue"
          :placeholder="searchPlaceholder"
          @search="handleSearch"
          @change="handleSearchChange"
          allowClear
        />
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-section">
        <a-button type="text" @click="showFilterDrawer = true">
          <a-icon type="filter" />
          筛选
        </a-button>
        <a-button type="text" @click="showStatusDrawer = true">
          <a-icon type="tags" />
          状态
        </a-button>
        <a-button type="primary" @click="handleAdd">
          <a-icon type="plus" />
          新建
        </a-button>
      </div>
    </div>

    <!-- 订单列表 -->
    <div class="orders-content">
      <mobile-list
        :dataSource="orderList"
        :loading="loading"
        :showHeader="false"
        :showSearch="false"
        :showActions="true"
        :showAvatar="false"
        :selectable="selectionMode"
        :selectedItems="selectedOrders"
        :pagination="pagination"
        :itemActions="itemActions"
        titleField="title"
        descriptionField="description"
        timeField="timeText"
        tagsField="tags"
        extraField="amountInfo"
        @itemClick="handleItemClick"
        @selectionChange="handleSelectionChange"
        @actionClick="handleActionClick"
        @pageChange="handlePageChange"
        @refresh="loadOrderList"
      >
        <!-- 自定义订单项内容 -->
        <template slot="item" slot-scope="{ item }">
          <div class="order-item-content">
            <!-- 订单头部 -->
            <div class="order-item-header">
              <div class="order-number">{{ item.number }}</div>
              <div class="order-status" :class="`status-${item.status}`">
                {{ getStatusText(item.status) }}
              </div>
            </div>
            
            <!-- 客户/供应商信息 -->
            <div class="order-item-partner">
              <span class="partner-label">{{ activeOrderType === 'sale' ? '客户' : '供应商' }}:</span>
              <span class="partner-name">{{ item.partnerName || '无' }}</span>
            </div>
            
            <!-- 订单金额 -->
            <div class="order-item-amount">
              <div class="amount-row">
                <span class="amount-label">订单金额:</span>
                <span class="amount-value">¥{{ formatMoney(item.totalPrice) }}</span>
              </div>
              <div class="amount-row" v-if="item.discountMoney > 0">
                <span class="amount-label">优惠金额:</span>
                <span class="amount-value discount">-¥{{ formatMoney(item.discountMoney) }}</span>
              </div>
            </div>
            
            <!-- 订单时间和操作人 -->
            <div class="order-item-footer">
              <div class="order-time">
                <a-icon type="clock-circle" />
                {{ formatTime(item.operTime) }}
              </div>
              <div class="order-operator">
                <a-icon type="user" />
                {{ item.operPersonName || '系统' }}
              </div>
            </div>
            
            <!-- 订单标签 -->
            <div class="order-item-tags" v-if="item.tags && item.tags.length">
              <a-tag
                v-for="tag in item.tags"
                :key="tag.key"
                :color="tag.color"
                size="small"
              >
                {{ tag.label }}
              </a-tag>
            </div>
          </div>
        </template>
      </mobile-list>
    </div>

    <!-- 底部批量操作栏 -->
    <div v-if="selectionMode && selectedOrders.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedOrders.length }} 项
      </div>
      <div class="batch-buttons">
        <a-button @click="batchApprove" v-if="canBatchApprove">批量审核</a-button>
        <a-button @click="batchReject" v-if="canBatchReject">批量驳回</a-button>
        <a-button @click="batchExport">导出</a-button>
        <a-button type="danger" @click="batchDelete" v-if="canBatchDelete">删除</a-button>
      </div>
    </div>

    <!-- 筛选抽屉 -->
    <mobile-drawer
      v-model="showFilterDrawer"
      title="筛选条件"
      placement="bottom"
      :showFooter="true"
      @confirm="applyFilter"
      @cancel="resetFilter"
    >
      <mobile-form
        :fields="filterFields"
        :model="filterModel"
        @change="handleFilterChange"
      />
    </mobile-drawer>

    <!-- 状态筛选抽屉 -->
    <mobile-drawer
      v-model="showStatusDrawer"
      title="订单状态"
      placement="right"
      :width="280"
    >
      <div class="status-list">
        <div 
          v-for="status in statusOptions" 
          :key="status.value"
          class="status-item"
          :class="{ active: filterModel.status === status.value }"
          @click="selectStatus(status.value)"
        >
          <div class="status-indicator" :class="`status-${status.value}`"></div>
          <div class="status-info">
            <div class="status-name">{{ status.label }}</div>
            <div class="status-count">{{ status.count || 0 }} 个订单</div>
          </div>
        </div>
      </div>
    </mobile-drawer>

    <!-- 订单详情/编辑模态框 -->
    <mobile-modal
      v-model="showOrderModal"
      :title="orderModalTitle"
      :fullscreen="$isMobile"
      :showFooter="orderModalMode !== 'view'"
      @ok="handleOrderSave"
      @cancel="handleOrderCancel"
    >
      <order-form
        ref="orderForm"
        :mode="orderModalMode"
        :orderType="activeOrderType"
        :orderData="currentOrder"
        @change="handleOrderFormChange"
      />
    </mobile-modal>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { MobileList, MobileDrawer, MobileModal, MobileForm } from '@/components/mobile'
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
import OrderForm from './components/OrderForm'

export default {
  name: 'MobileOrders',
  mixins: [ResponsiveMixin],
  components: {
    MobileList,
    MobileDrawer,
    MobileModal,
    MobileForm,
    OrderForm
  },

  data() {
    return {
      // 订单类型
      activeOrderType: 'sale', // sale: 销售订单, purchase: 采购订单
      
      // 搜索和筛选
      searchValue: '',
      showFilterDrawer: false,
      showStatusDrawer: false,
      filterModel: {},
      
      // 列表数据
      orderList: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: false,
        showQuickJumper: false
      },
      
      // 选择模式
      selectionMode: false,
      selectedOrders: [],
      
      // 模态框
      showOrderModal: false,
      orderModalMode: 'add', // add, edit, view
      currentOrder: null,
      
      // 状态选项
      statusOptions: [],
      
      // 筛选字段配置
      filterFields: [],
      
      // 操作菜单
      itemActions: [
        { key: 'view', label: '查看详情', icon: 'eye' },
        { key: 'edit', label: '编辑', icon: 'edit', disabled: item => item.status !== '0' },
        { key: 'copy', label: '复制', icon: 'copy' },
        { key: 'approve', label: '审核', icon: 'check', disabled: item => item.status !== '0' },
        { key: 'reject', label: '驳回', icon: 'close', disabled: item => item.status !== '0' },
        { key: 'delete', label: '删除', icon: 'delete', disabled: item => item.status !== '0' }
      ]
    }
  },

  computed: {
    searchPlaceholder() {
      return this.activeOrderType === 'sale' ? '搜索销售订单号、客户名称' : '搜索采购订单号、供应商名称'
    },

    orderModalTitle() {
      const typeText = this.activeOrderType === 'sale' ? '销售订单' : '采购订单'
      const titleMap = {
        add: `新建${typeText}`,
        edit: `编辑${typeText}`,
        view: `${typeText}详情`
      }
      return titleMap[this.orderModalMode] || '订单信息'
    },

    // 批量操作权限
    canBatchApprove() {
      return this.selectedOrders.some(order => order.status === '0')
    },

    canBatchReject() {
      return this.selectedOrders.some(order => order.status === '0')
    },

    canBatchDelete() {
      return this.selectedOrders.every(order => order.status === '0')
    }
  },

  watch: {
    activeOrderType: {
      handler() {
        this.initFilterFields()
        this.loadStatusOptions()
        this.resetFilter()
        this.loadOrderList()
      },
      immediate: true
    }
  },

  created() {
    // 从路由参数获取订单类型
    if (this.$route.query.type) {
      this.activeOrderType = this.$route.query.type
    }
    
    // 如果是添加操作，直接打开添加模态框
    if (this.$route.query.action === 'add') {
      this.$nextTick(() => {
        this.handleAdd()
      })
    }
  },

  methods: {
    // 初始化筛选字段
    initFilterFields() {
      const commonFields = [
        {
          key: 'status',
          type: 'select',
          label: '订单状态',
          options: [],
          placeholder: '请选择状态'
        },
        {
          key: 'operTimeStart',
          type: 'date',
          label: '开始日期',
          placeholder: '请选择开始日期'
        },
        {
          key: 'operTimeEnd',
          type: 'date',
          label: '结束日期',
          placeholder: '请选择结束日期'
        }
      ]

      if (this.activeOrderType === 'sale') {
        this.filterFields = [
          {
            key: 'organId',
            type: 'select',
            label: '客户',
            options: [],
            placeholder: '请选择客户'
          },
          ...commonFields
        ]
      } else {
        this.filterFields = [
          {
            key: 'organId',
            type: 'select',
            label: '供应商',
            options: [],
            placeholder: '请选择供应商'
          },
          ...commonFields
        ]
      }
    },

    // 加载状态选项
    async loadStatusOptions() {
      try {
        const apiPath = this.activeOrderType === 'sale' ? '/depotHead/saleStatus' : '/depotHead/purchaseStatus'
        const res = await getAction(apiPath)
        if (res.success) {
          this.statusOptions = res.result.map(item => ({
            value: item.value,
            label: item.label,
            count: item.count || 0
          }))
          
          // 更新筛选字段选项
          const statusField = this.filterFields.find(f => f.key === 'status')
          if (statusField) {
            statusField.options = this.statusOptions
          }
        }
      } catch (error) {
        console.error('加载状态选项失败:', error)
      }
    },

    // 加载订单列表
    async loadOrderList() {
      this.loading = true
      try {
        const apiPath = this.activeOrderType === 'sale' ? '/depotHead/saleList' : '/depotHead/purchaseList'
        const params = {
          current: this.pagination.current,
          size: this.pagination.pageSize,
          search: this.searchValue,
          ...this.filterModel
        }
        
        const res = await getAction(apiPath, params)
        if (res.success) {
          this.orderList = res.result.records.map(item => ({
            ...item,
            timeText: this.formatTime(item.operTime),
            tags: this.getOrderTags(item),
            amountInfo: this.getAmountInfo(item),
            title: item.number,
            description: `${this.activeOrderType === 'sale' ? '客户' : '供应商'}: ${item.organName || '无'}`
          }))
          
          this.pagination.total = res.result.total
        }
      } catch (error) {
        console.error('加载订单列表失败:', error)
        this.$message.error('加载订单列表失败')
      } finally {
        this.loading = false
      }
    },

    // 处理订单类型变化
    handleOrderTypeChange(activeKey) {
      this.activeOrderType = activeKey
      this.selectedOrders = []
      this.selectionMode = false
    },

    // 处理搜索
    handleSearch(value) {
      this.searchValue = value
      this.pagination.current = 1
      this.loadOrderList()
    },

    // 处理搜索变化
    handleSearchChange(e) {
      if (!e.target.value) {
        this.handleSearch('')
      }
    },

    // 处理分页变化
    handlePageChange(page, pageSize) {
      this.pagination.current = page
      this.pagination.pageSize = pageSize
      this.loadOrderList()
    },

    // 处理订单项点击
    handleItemClick(item) {
      this.currentOrder = item
      this.orderModalMode = 'view'
      this.showOrderModal = true
    },

    // 处理操作点击
    handleActionClick(actionKey, item) {
      switch (actionKey) {
        case 'view':
          this.handleItemClick(item)
          break
        case 'edit':
          this.handleEdit(item)
          break
        case 'copy':
          this.handleCopy(item)
          break
        case 'approve':
          this.handleApprove(item)
          break
        case 'reject':
          this.handleReject(item)
          break
        case 'delete':
          this.handleDelete(item)
          break
      }
    },

    // 处理选择变化
    handleSelectionChange(selectedItems) {
      this.selectedOrders = selectedItems
    },

    // 添加订单
    handleAdd() {
      this.currentOrder = null
      this.orderModalMode = 'add'
      this.showOrderModal = true
    },

    // 编辑订单
    handleEdit(item) {
      this.currentOrder = item
      this.orderModalMode = 'edit'
      this.showOrderModal = true
    },

    // 复制订单
    handleCopy(item) {
      this.currentOrder = { ...item, id: null, number: null }
      this.orderModalMode = 'add'
      this.showOrderModal = true
    },

    // 审核订单
    handleApprove(item) {
      this.$confirm({
        title: '确认审核',
        content: `确定要审核订单"${item.number}"吗？`,
        onOk: async () => {
          try {
            const apiPath = this.activeOrderType === 'sale' ? '/depotHead/saleApprove' : '/depotHead/purchaseApprove'
            const res = await postAction(apiPath, { id: item.id })
            if (res.success) {
              this.$message.success('审核成功')
              this.loadOrderList()
            }
          } catch (error) {
            this.$message.error('审核失败')
          }
        }
      })
    },

    // 驳回订单
    handleReject(item) {
      this.$confirm({
        title: '确认驳回',
        content: `确定要驳回订单"${item.number}"吗？`,
        onOk: async () => {
          try {
            const apiPath = this.activeOrderType === 'sale' ? '/depotHead/saleReject' : '/depotHead/purchaseReject'
            const res = await postAction(apiPath, { id: item.id })
            if (res.success) {
              this.$message.success('驳回成功')
              this.loadOrderList()
            }
          } catch (error) {
            this.$message.error('驳回失败')
          }
        }
      })
    },

    // 删除订单
    handleDelete(item) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除订单"${item.number}"吗？`,
        onOk: async () => {
          try {
            const apiPath = this.activeOrderType === 'sale' ? '/depotHead/saleDelete' : '/depotHead/purchaseDelete'
            const res = await deleteAction(apiPath, { id: item.id })
            if (res.success) {
              this.$message.success('删除成功')
              this.loadOrderList()
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        }
      })
    },

    // 选择状态
    selectStatus(status) {
      this.filterModel.status = status
      this.showStatusDrawer = false
      this.applyFilter()
    },

    // 应用筛选
    applyFilter() {
      this.pagination.current = 1
      this.loadOrderList()
      this.showFilterDrawer = false
    },

    // 重置筛选
    resetFilter() {
      this.filterModel = {}
      this.loadOrderList()
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '未审核',
        '1': '已审核',
        '2': '已驳回',
        '3': '已完成'
      }
      return statusMap[status] || '未知'
    },

    // 获取订单标签
    getOrderTags(item) {
      const tags = []
      
      if (item.status === '0') {
        tags.push({ key: 'pending', label: '待审核', color: 'orange' })
      } else if (item.status === '1') {
        tags.push({ key: 'approved', label: '已审核', color: 'green' })
      } else if (item.status === '2') {
        tags.push({ key: 'rejected', label: '已驳回', color: 'red' })
      }
      
      if (item.discountMoney > 0) {
        tags.push({ key: 'discount', label: '有优惠', color: 'blue' })
      }
      
      return tags
    },

    // 获取金额信息
    getAmountInfo(item) {
      return `实付: ¥${this.formatMoney(item.changeAmount || item.totalPrice)}`
    },

    // 格式化金额
    formatMoney(amount) {
      return Number(amount || 0).toLocaleString()
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleDateString()
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/styles/mobile/index.less';

.mobile-orders {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: @background-color-light;
  
  .orders-header {
    background-color: white;
    border-bottom: 1px solid @border-color-light;
    
    .ant-tabs {
      .ant-tabs-bar {
        margin-bottom: 0;
        border-bottom: 1px solid @border-color-light;
      }
    }
    
    .search-section {
      padding: @spacing-md @spacing-lg;
    }
    
    .action-section {
      .d-flex();
      justify-content: space-between;
      align-items: center;
      padding: 0 @spacing-lg @spacing-md;
      
      .ant-btn {
        .flex-center();
        gap: @spacing-xs;
      }
    }
  }
  
  .orders-content {
    flex: 1;
    overflow: hidden;
    
    .order-item-content {
      width: 100%;
      
      .order-item-header {
        .flex-between();
        align-items: flex-start;
        margin-bottom: @spacing-sm;
        
        .order-number {
          font-size: @font-size-base;
          font-weight: @font-weight-medium;
          color: @text-color;
          flex: 1;
          .text-ellipsis();
        }
        
        .order-status {
          padding: @spacing-xs @spacing-sm;
          border-radius: @border-radius-sm;
          font-size: @font-size-xs;
          font-weight: @font-weight-medium;
          
          &.status-0 {
            background-color: #FEF3C7;
            color: #D97706;
          }
          
          &.status-1 {
            background-color: #F0FDF4;
            color: #16A34A;
          }
          
          &.status-2 {
            background-color: #FEF2F2;
            color: #DC2626;
          }
          
          &.status-3 {
            background-color: #F0F9FF;
            color: #0284C7;
          }
        }
      }
      
      .order-item-partner {
        margin-bottom: @spacing-sm;
        
        .partner-label {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
        
        .partner-name {
          font-size: @font-size-sm;
          color: @text-color;
          margin-left: @spacing-xs;
        }
      }
      
      .order-item-amount {
        margin-bottom: @spacing-sm;
        
        .amount-row {
          .flex-between();
          align-items: center;
          margin-bottom: @spacing-xs;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .amount-label {
            font-size: @font-size-sm;
            color: @text-color-secondary;
          }
          
          .amount-value {
            font-size: @font-size-sm;
            font-weight: @font-weight-medium;
            color: @text-color;
            
            &.discount {
              color: @error-color;
            }
          }
        }
      }
      
      .order-item-footer {
        .flex-between();
        align-items: center;
        margin-bottom: @spacing-sm;
        
        .order-time,
        .order-operator {
          .d-flex();
          align-items: center;
          gap: @spacing-xs;
          font-size: @font-size-xs;
          color: @text-color-tertiary;
        }
      }
      
      .order-item-tags {
        .ant-tag {
          margin-right: @spacing-xs;
          margin-bottom: @spacing-xs;
        }
      }
    }
  }
  
  .batch-actions {
    background-color: white;
    padding: @spacing-md @spacing-lg;
    border-top: 1px solid @border-color-light;
    .flex-between();
    align-items: center;
    
    .batch-info {
      font-size: @font-size-sm;
      color: @text-color-secondary;
    }
    
    .batch-buttons {
      .d-flex();
      gap: @spacing-sm;
      
      .ant-btn {
        font-size: @font-size-sm;
        padding: @spacing-xs @spacing-sm;
      }
    }
  }
  
  .status-list {
    padding: @spacing-md;
    
    .status-item {
      .d-flex();
      align-items: center;
      padding: @spacing-md;
      border-radius: @border-radius-sm;
      cursor: pointer;
      .touch-feedback();
      
      &:hover {
        background-color: @background-color-light;
      }
      
      &.active {
        background-color: @primary-color-light;
        
        .status-indicator {
          border-color: @primary-color;
        }
      }
      
      .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid @border-color-base;
        margin-right: @spacing-md;
        
        &.status-0 {
          background-color: #D97706;
        }
        
        &.status-1 {
          background-color: #16A34A;
        }
        
        &.status-2 {
          background-color: #DC2626;
        }
        
        &.status-3 {
          background-color: #0284C7;
        }
      }
      
      .status-info {
        flex: 1;
        
        .status-name {
          font-size: @font-size-base;
          color: @text-color;
          margin-bottom: @spacing-xs;
        }
        
        .status-count {
          font-size: @font-size-sm;
          color: @text-color-tertiary;
        }
      }
    }
  }
}
</style>
