<template>
  <div class="mobile-dashboard">
    <!-- 顶部统计卡片 -->
    <div class="dashboard-stats">
      <div class="stats-grid">
        <div class="stat-card" @click="navigateTo('/mobile/material')">
          <div class="stat-icon">
            <a-icon type="shopping" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.materialCount }}</div>
            <div class="stat-label">商品总数</div>
          </div>
        </div>
        
        <div class="stat-card" @click="navigateTo('/mobile/orders')">
          <div class="stat-icon">
            <a-icon type="file-text" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.orderCount }}</div>
            <div class="stat-label">今日订单</div>
          </div>
        </div>
        
        <div class="stat-card" @click="navigateTo('/mobile/inventory')">
          <div class="stat-icon">
            <a-icon type="database" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.lowStockCount }}</div>
            <div class="stat-label">库存预警</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <a-icon type="dollar" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatMoney(stats.todaySales) }}</div>
            <div class="stat-label">今日销售</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="dashboard-actions">
      <div class="section-title">快捷操作</div>
      <div class="action-grid">
        <div class="action-item" @click="quickAction('sale')">
          <div class="action-icon">
            <a-icon type="plus-circle" />
          </div>
          <div class="action-label">销售开单</div>
        </div>
        
        <div class="action-item" @click="quickAction('purchase')">
          <div class="action-icon">
            <a-icon type="shopping-cart" />
          </div>
          <div class="action-label">采购开单</div>
        </div>
        
        <div class="action-item" @click="quickAction('inventory')">
          <div class="action-icon">
            <a-icon type="audit" />
          </div>
          <div class="action-label">库存盘点</div>
        </div>
        
        <div class="action-item" @click="quickAction('material')">
          <div class="action-icon">
            <a-icon type="plus" />
          </div>
          <div class="action-label">添加商品</div>
        </div>
      </div>
    </div>

    <!-- 最近动态 -->
    <div class="dashboard-activities">
      <div class="section-title">
        最近动态
        <a-button type="link" size="small" @click="viewAllActivities">查看全部</a-button>
      </div>
      
      <mobile-list
        :dataSource="activities"
        :loading="activitiesLoading"
        :showHeader="false"
        :showSearch="false"
        :showActions="false"
        :showAvatar="true"
        :pagination="false"
        titleField="title"
        descriptionField="description"
        timeField="timeText"
        @itemClick="handleActivityClick"
      />
    </div>

    <!-- 销售趋势图表 -->
    <div class="dashboard-chart">
      <div class="section-title">销售趋势</div>
      <div class="chart-container">
        <div ref="salesChart" class="chart"></div>
      </div>
    </div>

    <!-- 待办事项 -->
    <div class="dashboard-todos">
      <div class="section-title">
        待办事项
        <a-badge :count="todos.length" />
      </div>
      
      <div class="todo-list">
        <div 
          v-for="todo in todos" 
          :key="todo.id"
          class="todo-item"
          @click="handleTodoClick(todo)"
        >
          <div class="todo-content">
            <div class="todo-title">{{ todo.title }}</div>
            <div class="todo-time">{{ todo.timeText }}</div>
          </div>
          <div class="todo-priority" :class="`priority-${todo.priority}`">
            {{ getPriorityText(todo.priority) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { MobileList } from '@/components/mobile'
import { getAction } from '@/api/manage'
import * as echarts from 'echarts'

export default {
  name: 'MobileDashboard',
  mixins: [ResponsiveMixin],
  components: {
    MobileList
  },
  
  data() {
    return {
      // 统计数据
      stats: {
        materialCount: 0,
        orderCount: 0,
        lowStockCount: 0,
        todaySales: 0
      },
      
      // 最近动态
      activities: [],
      activitiesLoading: false,
      
      // 待办事项
      todos: [],
      
      // 图表实例
      salesChart: null
    }
  },

  mounted() {
    this.loadDashboardData()
    this.initSalesChart()
  },

  beforeDestroy() {
    if (this.salesChart) {
      this.salesChart.dispose()
    }
  },

  methods: {
    // 加载仪表板数据
    async loadDashboardData() {
      try {
        await Promise.all([
          this.loadStats(),
          this.loadActivities(),
          this.loadTodos()
        ])
      } catch (error) {
        console.error('加载仪表板数据失败:', error)
        this.$message.error('加载数据失败')
      }
    },

    // 加载统计数据
    async loadStats() {
      try {
        const res = await getAction('/dashboard/stats')
        if (res.success) {
          this.stats = res.result
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 加载最近动态
    async loadActivities() {
      this.activitiesLoading = true
      try {
        const res = await getAction('/dashboard/activities', { limit: 10 })
        if (res.success) {
          this.activities = res.result.map(item => ({
            ...item,
            timeText: this.formatTime(item.createTime),
            avatar: this.getActivityAvatar(item.type)
          }))
        }
      } catch (error) {
        console.error('加载动态失败:', error)
      } finally {
        this.activitiesLoading = false
      }
    },

    // 加载待办事项
    async loadTodos() {
      try {
        const res = await getAction('/dashboard/todos')
        if (res.success) {
          this.todos = res.result.map(item => ({
            ...item,
            timeText: this.formatTime(item.dueTime)
          }))
        }
      } catch (error) {
        console.error('加载待办事项失败:', error)
      }
    },

    // 初始化销售趋势图表
    initSalesChart() {
      this.$nextTick(() => {
        if (this.$refs.salesChart) {
          this.salesChart = echarts.init(this.$refs.salesChart)
          this.updateSalesChart()
        }
      })
    },

    // 更新销售趋势图表
    async updateSalesChart() {
      try {
        const res = await getAction('/dashboard/sales-trend')
        if (res.success && this.salesChart) {
          const option = {
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: res.result.dates,
              axisLine: { lineStyle: { color: '#E5E7EB' } },
              axisLabel: { color: '#6B7280' }
            },
            yAxis: {
              type: 'value',
              axisLine: { lineStyle: { color: '#E5E7EB' } },
              axisLabel: { color: '#6B7280' },
              splitLine: { lineStyle: { color: '#F3F4F6' } }
            },
            series: [{
              data: res.result.values,
              type: 'line',
              smooth: true,
              lineStyle: { color: '#3B82F6', width: 3 },
              itemStyle: { color: '#3B82F6' },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0, y: 0, x2: 0, y2: 1,
                  colorStops: [
                    { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
                    { offset: 1, color: 'rgba(59, 130, 246, 0.05)' }
                  ]
                }
              }
            }]
          }
          this.salesChart.setOption(option)
        }
      } catch (error) {
        console.error('加载销售趋势失败:', error)
      }
    },

    // 导航到指定页面
    navigateTo(path) {
      this.$router.push(path)
    },

    // 快捷操作
    quickAction(type) {
      switch (type) {
        case 'sale':
          this.$router.push('/mobile/orders?type=sale&action=add')
          break
        case 'purchase':
          this.$router.push('/mobile/orders?type=purchase&action=add')
          break
        case 'inventory':
          this.$router.push('/mobile/inventory?action=add')
          break
        case 'material':
          this.$router.push('/mobile/material?action=add')
          break
      }
    },

    // 处理动态点击
    handleActivityClick(activity) {
      // 根据动态类型跳转到相应页面
      if (activity.linkUrl) {
        this.$router.push(activity.linkUrl)
      }
    },

    // 查看全部动态
    viewAllActivities() {
      this.$router.push('/mobile/activities')
    },

    // 处理待办事项点击
    handleTodoClick(todo) {
      if (todo.linkUrl) {
        this.$router.push(todo.linkUrl)
      }
    },

    // 获取优先级文本
    getPriorityText(priority) {
      const map = {
        high: '高',
        medium: '中',
        low: '低'
      }
      return map[priority] || '中'
    },

    // 获取动态头像
    getActivityAvatar(type) {
      const avatarMap = {
        order: '📋',
        material: '📦',
        inventory: '📊',
        financial: '💰'
      }
      return avatarMap[type] || '📋'
    },

    // 格式化金额
    formatMoney(amount) {
      if (!amount) return '¥0'
      return `¥${Number(amount).toLocaleString()}`
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      return `${Math.floor(diff / 86400000)}天前`
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/styles/mobile/index.less';

.mobile-dashboard {
  padding: @spacing-lg;
  background-color: @background-color-light;
  min-height: 100vh;
  
  .dashboard-stats {
    margin-bottom: @spacing-xl;
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: @spacing-md;
      
      .stat-card {
        .mobile-card();
        .d-flex();
        align-items: center;
        padding: @spacing-lg;
        .touch-feedback();
        cursor: pointer;
        
        .stat-icon {
          width: 48px;
          height: 48px;
          .flex-center();
          background: linear-gradient(135deg, @primary-color, @primary-color-light);
          border-radius: @border-radius-lg;
          margin-right: @spacing-md;
          
          .anticon {
            font-size: 24px;
            color: white;
          }
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: @font-size-xl;
            font-weight: @font-weight-bold;
            color: @text-color;
            line-height: 1.2;
          }
          
          .stat-label {
            font-size: @font-size-sm;
            color: @text-color-secondary;
            margin-top: @spacing-xs;
          }
        }
      }
    }
  }
  
  .dashboard-actions {
    margin-bottom: @spacing-xl;
    
    .section-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
      margin-bottom: @spacing-md;
    }
    
    .action-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: @spacing-md;
      
      .action-item {
        .mobile-card();
        .flex-center();
        flex-direction: column;
        padding: @spacing-lg @spacing-md;
        .touch-feedback();
        cursor: pointer;
        
        .action-icon {
          width: 40px;
          height: 40px;
          .flex-center();
          background-color: @primary-color-light;
          border-radius: @border-radius-md;
          margin-bottom: @spacing-sm;
          
          .anticon {
            font-size: 20px;
            color: @primary-color;
          }
        }
        
        .action-label {
          font-size: @font-size-sm;
          color: @text-color;
          text-align: center;
        }
      }
    }
  }
  
  .dashboard-activities {
    margin-bottom: @spacing-xl;
    
    .section-title {
      .flex-between();
      align-items: center;
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
      margin-bottom: @spacing-md;
    }
  }
  
  .dashboard-chart {
    margin-bottom: @spacing-xl;
    
    .section-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
      margin-bottom: @spacing-md;
    }
    
    .chart-container {
      .mobile-card();
      padding: @spacing-lg;
      
      .chart {
        width: 100%;
        height: 200px;
      }
    }
  }
  
  .dashboard-todos {
    .section-title {
      .flex-between();
      align-items: center;
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
      margin-bottom: @spacing-md;
    }
    
    .todo-list {
      .todo-item {
        .mobile-card();
        .flex-between();
        align-items: center;
        padding: @spacing-md @spacing-lg;
        margin-bottom: @spacing-sm;
        .touch-feedback();
        cursor: pointer;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .todo-content {
          flex: 1;
          
          .todo-title {
            font-size: @font-size-base;
            color: @text-color;
            margin-bottom: @spacing-xs;
          }
          
          .todo-time {
            font-size: @font-size-sm;
            color: @text-color-tertiary;
          }
        }
        
        .todo-priority {
          padding: @spacing-xs @spacing-sm;
          border-radius: @border-radius-sm;
          font-size: @font-size-xs;
          font-weight: @font-weight-medium;
          
          &.priority-high {
            background-color: #FEF2F2;
            color: #DC2626;
          }
          
          &.priority-medium {
            background-color: #FEF3C7;
            color: #D97706;
          }
          
          &.priority-low {
            background-color: #F0FDF4;
            color: #16A34A;
          }
        }
      }
    }
  }
}
</style>
