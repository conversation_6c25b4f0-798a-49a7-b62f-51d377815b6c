<template>
  <a-drawer
    v-bind="drawerProps"
    :visible="visible"
    :title="title"
    :width="drawerWidth"
    :height="drawerHeight"
    :placement="drawerPlacement"
    :closable="closable"
    :maskClosable="maskClosable"
    :keyboard="keyboard"
    :destroyOnClose="destroyOnClose"
    :getContainer="getContainer"
    :zIndex="zIndex"
    :bodyStyle="bodyStyle"
    :headerStyle="headerStyle"
    :drawerStyle="drawerStyle"
    :maskStyle="maskStyle"
    :wrapClassName="wrapClassName"
    :class="drawerClass"
    @close="handleClose"
    @afterVisibleChange="handleAfterVisibleChange"
  >
    <!-- 自定义标题 -->
    <template v-if="$slots.title" slot="title">
      <slot name="title" />
    </template>

    <!-- 抽屉内容 -->
    <div class="mobile-drawer-content">
      <slot />
    </div>

    <!-- 自定义底部 -->
    <template v-if="$slots.footer">
      <div class="mobile-drawer-footer">
        <slot name="footer" />
      </div>
    </template>
    
    <!-- 默认底部 -->
    <div v-else-if="showFooter" class="mobile-drawer-footer">
      <div class="mobile-drawer-actions">
        <a-button
          v-if="showCancel"
          :size="buttonSize"
          @click="handleCancel"
        >
          {{ cancelText }}
        </a-button>
        <a-button
          v-if="showConfirm"
          type="primary"
          :size="buttonSize"
          :loading="confirmLoading"
          @click="handleConfirm"
        >
          {{ confirmText }}
        </a-button>
      </div>
    </div>
  </a-drawer>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { ComponentAdapterFactory } from '@/utils/component-adapter'

export default {
  name: 'MobileDrawer',
  mixins: [ResponsiveMixin],
  props: {
    // 基础抽屉属性
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: [String, Number],
      default: 256
    },
    height: {
      type: [String, Number],
      default: 256
    },
    placement: {
      type: String,
      default: 'right',
      validator: value => ['top', 'right', 'bottom', 'left'].includes(value)
    },
    closable: {
      type: Boolean,
      default: true
    },
    maskClosable: {
      type: Boolean,
      default: true
    },
    keyboard: {
      type: Boolean,
      default: true
    },
    destroyOnClose: {
      type: Boolean,
      default: false
    },
    getContainer: {
      type: [String, Function],
      default: undefined
    },
    zIndex: {
      type: Number,
      default: 1000
    },
    bodyStyle: {
      type: Object,
      default: () => ({})
    },
    headerStyle: {
      type: Object,
      default: () => ({})
    },
    drawerStyle: {
      type: Object,
      default: () => ({})
    },
    maskStyle: {
      type: Object,
      default: () => ({})
    },
    wrapClassName: {
      type: String,
      default: ''
    },
    
    // 底部操作配置
    showFooter: {
      type: Boolean,
      default: false
    },
    showCancel: {
      type: Boolean,
      default: true
    },
    showConfirm: {
      type: Boolean,
      default: true
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    confirmText: {
      type: String,
      default: '确定'
    },
    confirmLoading: {
      type: Boolean,
      default: false
    },
    
    // 移动端特有配置
    fullscreen: {
      type: Boolean,
      default: false
    },
    showHandle: {
      type: Boolean,
      default: true
    }
  },

  computed: {
    // 抽屉适配器
    drawerAdapter() {
      return ComponentAdapterFactory.create('drawer')
    },

    // 适配后的抽屉属性
    drawerProps() {
      const baseProps = this.drawerAdapter.getAdaptedProps()
      return baseProps
    },

    // 抽屉宽度
    drawerWidth() {
      if (this.$isMobile) {
        if (this.fullscreen) {
          return '100%'
        }
        if (this.placement === 'left' || this.placement === 'right') {
          return '85%'
        }
      }
      return this.width
    },

    // 抽屉高度
    drawerHeight() {
      if (this.$isMobile) {
        if (this.fullscreen) {
          return '100%'
        }
        if (this.placement === 'top' || this.placement === 'bottom') {
          return '70%'
        }
      }
      return this.height
    },

    // 抽屉位置
    drawerPlacement() {
      if (this.$isMobile) {
        // 移动端默认从底部弹出
        return this.placement === 'right' ? 'bottom' : this.placement
      }
      return this.placement
    },

    // 抽屉样式类
    drawerClass() {
      return [
        'mobile-drawer',
        {
          'mobile-drawer-fullscreen': this.$isMobile && this.fullscreen,
          'mobile-drawer-mobile': this.$isMobile,
          [`mobile-drawer-${this.drawerPlacement}`]: true
        },
        this.wrapClassName
      ]
    },

    // 按钮尺寸
    buttonSize() {
      return this.$isMobile ? 'large' : 'default'
    }
  },

  methods: {
    // 处理关闭
    handleClose(e) {
      this.$emit('close', e)
    },

    // 处理可见性变化后
    handleAfterVisibleChange(visible) {
      this.$emit('afterVisibleChange', visible)
    },

    // 处理取消
    handleCancel() {
      this.$emit('cancel')
      this.handleClose()
    },

    // 处理确认
    handleConfirm() {
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/styles/mobile/index.less';

// 移动端抽屉样式
.mobile-drawer {
  &.mobile-drawer-mobile {
    :global(.ant-drawer-content-wrapper) {
      .ant-drawer-content {
        display: flex;
        flex-direction: column;
        
        .ant-drawer-header {
          flex-shrink: 0;
          padding: @spacing-lg;
          border-bottom: 1px solid @border-color-light;
          
          .ant-drawer-title {
            font-size: @font-size-lg;
            font-weight: @font-weight-semibold;
            text-align: center;
          }
          
          .ant-drawer-close {
            top: 50%;
            transform: translateY(-50%);
            right: @spacing-md;
            width: @touch-target-min;
            height: @touch-target-min;
            .flex-center();
            
            .anticon {
              font-size: @font-size-lg;
            }
          }
        }
        
        .ant-drawer-body {
          flex: 1;
          padding: 0;
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;
          
          .mobile-drawer-content {
            padding: @spacing-lg;
            min-height: 100%;
          }
        }
        
        .mobile-drawer-footer {
          flex-shrink: 0;
          padding: @spacing-lg;
          border-top: 1px solid @border-color-light;
          background-color: @background-color-light;
          
          .mobile-drawer-actions {
            .d-flex();
            gap: @spacing-md;
            
            .ant-btn {
              flex: 1;
              min-height: @touch-target-min;
              border-radius: @border-radius-md;
              font-size: @font-size-base;
            }
          }
        }
      }
    }
  }
  
  // 全屏抽屉
  &.mobile-drawer-fullscreen {
    :global(.ant-drawer-content-wrapper) {
      width: 100% !important;
      height: 100% !important;
      
      .ant-drawer-content {
        border-radius: 0;
      }
    }
  }
  
  // 底部抽屉
  &.mobile-drawer-bottom {
    :global(.ant-drawer-content-wrapper) {
      .ant-drawer-content {
        border-radius: @border-radius-lg @border-radius-lg 0 0;
        
        // 添加拖拽指示器
        &::before {
          content: '';
          position: absolute;
          top: @spacing-sm;
          left: 50%;
          transform: translateX(-50%);
          width: 40px;
          height: 4px;
          background-color: @border-color-base;
          border-radius: 2px;
          z-index: 1;
        }
        
        .ant-drawer-header {
          padding-top: (@spacing-lg + @spacing-sm);
        }
      }
    }
  }
  
  // 右侧抽屉
  &.mobile-drawer-right {
    :global(.ant-drawer-content-wrapper) {
      .ant-drawer-content {
        border-radius: @border-radius-lg 0 0 @border-radius-lg;
      }
    }
  }
  
  // 左侧抽屉
  &.mobile-drawer-left {
    :global(.ant-drawer-content-wrapper) {
      .ant-drawer-content {
        border-radius: 0 @border-radius-lg @border-radius-lg 0;
      }
    }
  }
  
  // 顶部抽屉
  &.mobile-drawer-top {
    :global(.ant-drawer-content-wrapper) {
      .ant-drawer-content {
        border-radius: 0 0 @border-radius-lg @border-radius-lg;
      }
    }
  }
}

// 桌面端样式保持不变
.desktop-only {
  .mobile-drawer {
    :global(.ant-drawer-content-wrapper) {
      .ant-drawer-content {
        border-radius: 0;
        
        &::before {
          display: none;
        }
        
        .ant-drawer-header {
          padding: @spacing-lg @spacing-xl;
          
          .ant-drawer-title {
            text-align: left;
            font-size: @font-size-lg;
          }
        }
        
        .ant-drawer-body {
          .mobile-drawer-content {
            padding: @spacing-lg @spacing-xl;
          }
        }
        
        .mobile-drawer-footer {
          padding: @spacing-lg @spacing-xl;
          
          .mobile-drawer-actions {
            justify-content: flex-end;
            
            .ant-btn {
              flex: none;
              min-width: 80px;
            }
          }
        }
      }
    }
  }
}

// 抽屉动画优化
:global(.mobile-drawer-mobile) {
  &.ant-drawer-open {
    .ant-drawer-mask {
      background-color: rgba(0, 0, 0, 0.6);
    }
  }
  
  .ant-drawer-content-wrapper {
    transition: all @animation-duration-mobile-base @ease-out;
  }
}

// 底部抽屉特殊动画
:global(.mobile-drawer-bottom) {
  .ant-drawer-content-wrapper {
    animation-duration: @animation-duration-mobile-base;
    animation-timing-function: @ease-out;
  }
}
</style>
