<template>
  <a-form-item
    :label="field.label"
    :required="field.required"
    :help="field.help"
    :validateStatus="field.validateStatus"
    :hasFeedback="field.hasFeedback"
    :extra="field.extra"
    :colon="field.colon"
    :labelAlign="field.labelAlign"
    :labelCol="field.labelCol"
    :wrapperCol="field.wrapperCol"
    :class="formItemClass"
  >
    <!-- 输入框 -->
    <a-input
      v-if="field.type === 'input'"
      v-decorator="[field.key, field.decorator || {}]"
      :placeholder="field.placeholder"
      :disabled="disabled || field.disabled"
      :size="inputSize"
      :maxLength="field.maxLength"
      :allowClear="field.allowClear !== false"
      :addonBefore="field.addonBefore"
      :addonAfter="field.addonAfter"
      :prefix="field.prefix"
      :suffix="field.suffix"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
      @pressEnter="handlePressEnter"
    />

    <!-- 密码输入框 -->
    <a-input-password
      v-else-if="field.type === 'password'"
      v-decorator="[field.key, field.decorator || {}]"
      :placeholder="field.placeholder"
      :disabled="disabled || field.disabled"
      :size="inputSize"
      :maxLength="field.maxLength"
      :allowClear="field.allowClear !== false"
      :visibilityToggle="field.visibilityToggle !== false"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
      @pressEnter="handlePressEnter"
    />

    <!-- 文本域 -->
    <a-textarea
      v-else-if="field.type === 'textarea'"
      v-decorator="[field.key, field.decorator || {}]"
      :placeholder="field.placeholder"
      :disabled="disabled || field.disabled"
      :rows="field.rows || 4"
      :maxLength="field.maxLength"
      :allowClear="field.allowClear !== false"
      :autoSize="field.autoSize"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
    />

    <!-- 数字输入框 -->
    <a-input-number
      v-else-if="field.type === 'number'"
      v-decorator="[field.key, field.decorator || {}]"
      :placeholder="field.placeholder"
      :disabled="disabled || field.disabled"
      :size="inputSize"
      :min="field.min"
      :max="field.max"
      :step="field.step"
      :precision="field.precision"
      :formatter="field.formatter"
      :parser="field.parser"
      style="width: 100%"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
    />

    <!-- 选择器 -->
    <a-select
      v-else-if="field.type === 'select'"
      v-decorator="[field.key, field.decorator || {}]"
      :placeholder="field.placeholder"
      :disabled="disabled || field.disabled"
      :size="inputSize"
      :mode="field.mode"
      :allowClear="field.allowClear !== false"
      :showSearch="field.showSearch"
      :filterOption="field.filterOption"
      :optionFilterProp="field.optionFilterProp"
      :loading="field.loading"
      :dropdownMatchSelectWidth="field.dropdownMatchSelectWidth !== false"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
      @search="handleSearch"
    >
      <a-select-option
        v-for="option in field.options"
        :key="option.value"
        :value="option.value"
        :disabled="option.disabled"
      >
        {{ option.label }}
      </a-select-option>
    </a-select>

    <!-- 级联选择器 -->
    <a-cascader
      v-else-if="field.type === 'cascader'"
      v-decorator="[field.key, field.decorator || {}]"
      :placeholder="field.placeholder"
      :disabled="disabled || field.disabled"
      :size="inputSize"
      :options="field.options"
      :allowClear="field.allowClear !== false"
      :showSearch="field.showSearch"
      :changeOnSelect="field.changeOnSelect"
      :displayRender="field.displayRender"
      :fieldNames="field.fieldNames"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
    />

    <!-- 日期选择器 -->
    <a-date-picker
      v-else-if="field.type === 'date'"
      v-decorator="[field.key, field.decorator || {}]"
      :placeholder="field.placeholder"
      :disabled="disabled || field.disabled"
      :size="inputSize"
      :format="field.format"
      :allowClear="field.allowClear !== false"
      :showTime="field.showTime"
      :disabledDate="field.disabledDate"
      style="width: 100%"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
    />

    <!-- 日期范围选择器 -->
    <a-range-picker
      v-else-if="field.type === 'dateRange'"
      v-decorator="[field.key, field.decorator || {}]"
      :placeholder="field.placeholder"
      :disabled="disabled || field.disabled"
      :size="inputSize"
      :format="field.format"
      :allowClear="field.allowClear !== false"
      :showTime="field.showTime"
      :disabledDate="field.disabledDate"
      style="width: 100%"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
    />

    <!-- 时间选择器 -->
    <a-time-picker
      v-else-if="field.type === 'time'"
      v-decorator="[field.key, field.decorator || {}]"
      :placeholder="field.placeholder"
      :disabled="disabled || field.disabled"
      :size="inputSize"
      :format="field.format"
      :allowClear="field.allowClear !== false"
      :hourStep="field.hourStep"
      :minuteStep="field.minuteStep"
      :secondStep="field.secondStep"
      style="width: 100%"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
    />

    <!-- 单选框组 -->
    <a-radio-group
      v-else-if="field.type === 'radio'"
      v-decorator="[field.key, field.decorator || {}]"
      :disabled="disabled || field.disabled"
      :size="inputSize"
      :buttonStyle="field.buttonStyle"
      @change="handleChange"
    >
      <template v-if="field.buttonStyle === 'solid'">
        <a-radio-button
          v-for="option in field.options"
          :key="option.value"
          :value="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </a-radio-button>
      </template>
      <template v-else>
        <a-radio
          v-for="option in field.options"
          :key="option.value"
          :value="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </a-radio>
      </template>
    </a-radio-group>

    <!-- 复选框组 -->
    <a-checkbox-group
      v-else-if="field.type === 'checkbox'"
      v-decorator="[field.key, field.decorator || {}]"
      :disabled="disabled || field.disabled"
      :options="field.options"
      @change="handleChange"
    />

    <!-- 单个复选框 -->
    <a-checkbox
      v-else-if="field.type === 'checkboxSingle'"
      v-decorator="[field.key, field.decorator || {}]"
      :disabled="disabled || field.disabled"
      @change="handleChange"
    >
      {{ field.checkboxText }}
    </a-checkbox>

    <!-- 开关 -->
    <a-switch
      v-else-if="field.type === 'switch'"
      v-decorator="[field.key, field.decorator || {}]"
      :disabled="disabled || field.disabled"
      :size="field.size || 'default'"
      :checkedChildren="field.checkedChildren"
      :unCheckedChildren="field.unCheckedChildren"
      @change="handleChange"
    />

    <!-- 滑块 -->
    <a-slider
      v-else-if="field.type === 'slider'"
      v-decorator="[field.key, field.decorator || {}]"
      :disabled="disabled || field.disabled"
      :min="field.min"
      :max="field.max"
      :step="field.step"
      :marks="field.marks"
      :range="field.range"
      :vertical="field.vertical"
      @change="handleChange"
    />

    <!-- 评分 -->
    <a-rate
      v-else-if="field.type === 'rate'"
      v-decorator="[field.key, field.decorator || {}]"
      :disabled="disabled || field.disabled"
      :count="field.count"
      :allowHalf="field.allowHalf"
      :allowClear="field.allowClear"
      :character="field.character"
      @change="handleChange"
    />

    <!-- 上传 -->
    <a-upload
      v-else-if="field.type === 'upload'"
      v-decorator="[field.key, field.decorator || {}]"
      :disabled="disabled || field.disabled"
      :action="field.action"
      :headers="field.headers"
      :data="field.data"
      :name="field.name"
      :multiple="field.multiple"
      :accept="field.accept"
      :listType="field.listType"
      :beforeUpload="field.beforeUpload"
      :customRequest="field.customRequest"
      @change="handleUploadChange"
    >
      <a-button v-if="field.listType !== 'picture-card'" :icon="field.icon || 'upload'">
        {{ field.uploadText || '点击上传' }}
      </a-button>
      <div v-else class="upload-button">
        <a-icon :type="field.icon || 'plus'" />
        <div class="upload-text">{{ field.uploadText || '上传' }}</div>
      </div>
    </a-upload>

    <!-- 自定义组件 -->
    <component
      v-else-if="field.type === 'custom' && field.component"
      :is="field.component"
      v-decorator="[field.key, field.decorator || {}]"
      v-bind="field.props"
      :disabled="disabled || field.disabled"
      @change="handleChange"
    />

    <!-- 插槽内容 -->
    <template v-else-if="field.type === 'slot'">
      <slot :name="field.slotName" :field="field" :form="form" :disabled="disabled" />
    </template>

    <!-- 静态文本 -->
    <span v-else-if="field.type === 'text'" class="form-text">
      {{ field.text }}
    </span>

    <!-- 分割线 -->
    <a-divider v-else-if="field.type === 'divider'" :type="field.dividerType">
      {{ field.text }}
    </a-divider>

    <!-- 默认输入框 -->
    <a-input
      v-else
      v-decorator="[field.key, field.decorator || {}]"
      :placeholder="field.placeholder"
      :disabled="disabled || field.disabled"
      :size="inputSize"
      @change="handleChange"
    />
  </a-form-item>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'

export default {
  name: 'MobileFormItem',
  mixins: [ResponsiveMixin],
  props: {
    field: {
      type: Object,
      required: true
    },
    form: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    // 表单项样式类
    formItemClass() {
      return [
        'mobile-form-item',
        `mobile-form-item-${this.field.type}`,
        {
          'mobile-form-item-required': this.field.required,
          'mobile-form-item-disabled': this.disabled || this.field.disabled,
          'mobile-form-item-mobile': this.$isMobile
        }
      ]
    },

    // 输入框尺寸
    inputSize() {
      return this.$isMobile ? 'large' : (this.field.size || 'default')
    }
  },

  methods: {
    // 处理值变化
    handleChange(value) {
      this.$emit('change', this.field, value)
    },

    // 处理失焦
    handleBlur(e) {
      this.$emit('blur', this.field, e)
    },

    // 处理聚焦
    handleFocus(e) {
      this.$emit('focus', this.field, e)
    },

    // 处理回车
    handlePressEnter(e) {
      this.$emit('pressEnter', this.field, e)
    },

    // 处理搜索
    handleSearch(value) {
      this.$emit('search', this.field, value)
    },

    // 处理上传变化
    handleUploadChange(info) {
      this.$emit('uploadChange', this.field, info)
      this.handleChange(info)
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/styles/mobile/index.less';

.mobile-form-item {
  &.mobile-form-item-mobile {
    .ant-form-item-label {
      padding-bottom: @spacing-sm;
      
      label {
        font-size: @font-size-base;
        font-weight: @font-weight-medium;
        color: @text-color;
      }
    }
    
    .ant-form-item-control {
      .ant-input,
      .ant-select-selector,
      .ant-picker,
      .ant-input-number,
      .ant-cascader-picker {
        min-height: @touch-target-min;
        font-size: @font-size-base;
        border-radius: @border-radius-md;
        
        // 防止iOS缩放
        &:focus {
          font-size: @font-size-base;
        }
      }
      
      .ant-input-affix-wrapper {
        min-height: @touch-target-min;
        border-radius: @border-radius-md;
        
        .ant-input {
          min-height: auto;
          font-size: @font-size-base;
        }
      }
      
      .ant-select {
        .ant-select-selector {
          min-height: @touch-target-min;
          border-radius: @border-radius-md;
          
          .ant-select-selection-item {
            line-height: (@touch-target-min - 2px);
            font-size: @font-size-base;
          }
          
          .ant-select-selection-placeholder {
            line-height: (@touch-target-min - 2px);
            font-size: @font-size-base;
          }
        }
      }
      
      .ant-checkbox-wrapper,
      .ant-radio-wrapper {
        min-height: @touch-target-min;
        .flex-center();
        font-size: @font-size-base;
        
        .ant-checkbox,
        .ant-radio {
          margin-right: @spacing-sm;
          
          .ant-checkbox-inner,
          .ant-radio-inner {
            width: 20px;
            height: 20px;
          }
        }
      }
      
      .ant-checkbox-group,
      .ant-radio-group {
        .ant-checkbox-wrapper,
        .ant-radio-wrapper {
          margin-bottom: @spacing-sm;
          margin-right: @spacing-lg;
          width: 100%;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      
      .ant-switch {
        min-height: @touch-target-min;
        .flex-center();
      }
      
      .ant-slider {
        margin: @spacing-md 0;
      }
      
      .ant-rate {
        font-size: 20px;
        
        .ant-rate-star {
          margin-right: @spacing-sm;
        }
      }
      
      .ant-upload {
        &.ant-upload-select-picture-card {
          width: 100px;
          height: 100px;
          
          .upload-button {
            .flex-center();
            flex-direction: column;
            height: 100%;
            
            .anticon {
              font-size: 24px;
              margin-bottom: @spacing-xs;
            }
            
            .upload-text {
              font-size: @font-size-sm;
            }
          }
        }
      }
    }
    
    .ant-form-item-explain {
      margin-top: @spacing-xs;
      font-size: @font-size-sm;
      color: @text-color-secondary;
    }
    
    .ant-form-item-extra {
      margin-top: @spacing-xs;
      font-size: @font-size-sm;
      color: @text-color-tertiary;
    }
  }
  
  &.mobile-form-item-text {
    .form-text {
      font-size: @font-size-base;
      color: @text-color;
      line-height: @touch-target-min;
      .flex-center();
    }
  }
  
  &.mobile-form-item-divider {
    .ant-divider {
      margin: @spacing-lg 0;
      font-size: @font-size-base;
      font-weight: @font-weight-medium;
    }
  }
  
  &.mobile-form-item-disabled {
    .ant-form-item-control {
      opacity: 0.6;
    }
  }
}
</style>
