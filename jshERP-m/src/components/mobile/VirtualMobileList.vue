<template>
  <div class="virtual-mobile-list" :class="responsiveClass">
    <!-- 列表头部 -->
    <div v-if="showHeader" class="mobile-list-header">
      <div class="mobile-list-title">
        <slot name="title">{{ title }}</slot>
      </div>
      <div class="mobile-list-actions">
        <slot name="actions">
          <a-button 
            v-if="showRefresh" 
            type="text" 
            size="small" 
            @click="handleRefresh"
            :loading="loading"
          >
            <a-icon type="reload" />
          </a-button>
        </slot>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div v-if="showSearch" class="mobile-list-search">
      <a-input-search
        v-model="searchValue"
        :placeholder="searchPlaceholder"
        @search="handleSearch"
        @change="handleSearchChange"
        allowClear
      />
    </div>

    <!-- 虚拟滚动容器 -->
    <div 
      ref="scrollContainer"
      class="virtual-scroll-container"
      :style="{ height: containerHeight + 'px' }"
      @scroll="handleScroll"
    >
      <!-- 占位容器 - 用于维持滚动条高度 -->
      <div 
        class="virtual-scroll-placeholder"
        :style="{ height: totalHeight + 'px' }"
      ></div>
      
      <!-- 可视区域内容 -->
      <div 
        class="virtual-scroll-content"
        :style="{ 
          transform: `translateY(${offsetY}px)`,
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0
        }"
      >
        <!-- 加载状态 -->
        <div v-if="loading" class="mobile-list-loading">
          <a-spin size="large" />
        </div>
        
        <!-- 空状态 -->
        <div v-else-if="dataSource.length === 0" class="mobile-list-empty">
          <a-empty :description="emptyText" />
        </div>
        
        <!-- 虚拟列表项 -->
        <div v-else class="virtual-list-items">
          <div
            v-for="(item, index) in visibleItems"
            :key="getItemKey(item.data, item.index)"
            class="mobile-list-item"
            :class="getItemClass(item.data, item.index)"
            :style="{ height: itemHeight + 'px' }"
            @click="handleItemClick(item.data, item.index)"
          >
            <!-- 左侧图标/头像 -->
            <div v-if="showAvatar || $slots.avatar" class="mobile-item-avatar">
              <slot name="avatar" :item="item.data" :index="item.index">
                <a-avatar
                  v-if="getItemAvatar(item.data)"
                  :src="getItemAvatar(item.data)"
                  :size="avatarSize"
                />
                <a-avatar
                  v-else
                  :size="avatarSize"
                  :style="{ backgroundColor: getAvatarColor(item.data) }"
                >
                  {{ getAvatarText(item.data) }}
                </a-avatar>
              </slot>
            </div>

            <!-- 主要内容 -->
            <div class="mobile-item-content">
              <slot name="item" :item="item.data" :index="item.index">
                <!-- 标题行 -->
                <div class="mobile-item-title">
                  <span class="title-text">{{ getItemTitle(item.data) }}</span>
                  <span v-if="getItemTime(item.data)" class="title-time">
                    {{ getItemTime(item.data) }}
                  </span>
                </div>
                
                <!-- 描述行 -->
                <div v-if="getItemDescription(item.data)" class="mobile-item-description">
                  {{ getItemDescription(item.data) }}
                </div>
                
                <!-- 标签行 -->
                <div v-if="getItemTags(item.data).length > 0" class="mobile-item-tags">
                  <a-tag
                    v-for="tag in getItemTags(item.data)"
                    :key="tag.key || tag"
                    :color="tag.color"
                    size="small"
                  >
                    {{ tag.label || tag }}
                  </a-tag>
                </div>
              </slot>
            </div>

            <!-- 右侧操作 -->
            <div v-if="showActions || $slots.actions" class="mobile-item-actions">
              <slot name="actions" :item="item.data" :index="item.index">
                <a-dropdown v-if="itemActions.length > 0" placement="bottomRight">
                  <a-button type="text" size="small">
                    <a-icon type="more" />
                  </a-button>
                  <a-menu slot="overlay" @click="handleActionClick($event, item.data, item.index)">
                    <a-menu-item
                      v-for="action in itemActions"
                      :key="action.key"
                      :disabled="action.disabled && action.disabled(item.data)"
                    >
                      <a-icon v-if="action.icon" :type="action.icon" />
                      {{ action.label }}
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
              </slot>
            </div>

            <!-- 选择框 -->
            <div v-if="selectable" class="mobile-item-selection">
              <a-checkbox
                :checked="isItemSelected(item.data)"
                @change="handleItemSelect(item.data, $event)"
                @click.stop
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="pagination && dataSource.length > 0" class="mobile-list-pagination">
      <a-pagination
        v-bind="paginationProps"
        @change="handlePageChange"
        @showSizeChange="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'

export default {
  name: 'VirtualMobileList',
  mixins: [ResponsiveMixin],
  
  props: {
    // 数据源
    dataSource: {
      type: Array,
      default: () => []
    },
    
    // 列表项高度（固定高度，用于虚拟滚动计算）
    itemHeight: {
      type: Number,
      default: 80
    },
    
    // 容器高度
    containerHeight: {
      type: Number,
      default: 400
    },
    
    // 缓冲区大小（可视区域外渲染的项目数量）
    bufferSize: {
      type: Number,
      default: 5
    },
    
    // 基础属性
    title: String,
    loading: Boolean,
    showHeader: {
      type: Boolean,
      default: true
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    showRefresh: {
      type: Boolean,
      default: true
    },
    showActions: {
      type: Boolean,
      default: true
    },
    showAvatar: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Boolean,
      default: false
    },
    
    // 字段映射
    titleField: {
      type: String,
      default: 'title'
    },
    descriptionField: {
      type: String,
      default: 'description'
    },
    timeField: {
      type: String,
      default: 'time'
    },
    tagsField: {
      type: String,
      default: 'tags'
    },
    avatarField: {
      type: String,
      default: 'avatar'
    },
    
    // 操作配置
    itemActions: {
      type: Array,
      default: () => []
    },
    
    // 分页配置
    pagination: {
      type: [Object, Boolean],
      default: false
    },
    
    // 选中项
    selectedItems: {
      type: Array,
      default: () => []
    },
    
    // 搜索配置
    searchPlaceholder: {
      type: String,
      default: '搜索...'
    },
    
    // 空状态
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    
    // 头像配置
    avatarSize: {
      type: [String, Number],
      default: 40
    }
  },

  data() {
    return {
      searchValue: '',
      scrollTop: 0,
      startIndex: 0,
      endIndex: 0,
      visibleItems: []
    }
  },

  computed: {
    // 总高度
    totalHeight() {
      return this.dataSource.length * this.itemHeight
    },
    
    // 可视区域可容纳的项目数量
    visibleCount() {
      return Math.ceil(this.containerHeight / this.itemHeight)
    },
    
    // 偏移量
    offsetY() {
      return this.startIndex * this.itemHeight
    },
    
    // 分页属性
    paginationProps() {
      if (!this.pagination) return {}
      
      return {
        size: 'small',
        showSizeChanger: false,
        showQuickJumper: false,
        ...this.pagination
      }
    }
  },

  watch: {
    dataSource: {
      handler() {
        this.updateVisibleItems()
      },
      immediate: true
    },
    
    scrollTop() {
      this.updateVisibleItems()
    }
  },

  methods: {
    // 处理滚动事件
    handleScroll(event) {
      this.scrollTop = event.target.scrollTop
    },
    
    // 更新可视项目
    updateVisibleItems() {
      if (this.dataSource.length === 0) {
        this.visibleItems = []
        return
      }
      
      // 计算可视区域的开始和结束索引
      this.startIndex = Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - this.bufferSize)
      this.endIndex = Math.min(
        this.dataSource.length - 1,
        this.startIndex + this.visibleCount + this.bufferSize * 2
      )
      
      // 生成可视项目
      this.visibleItems = []
      for (let i = this.startIndex; i <= this.endIndex; i++) {
        this.visibleItems.push({
          index: i,
          data: this.dataSource[i]
        })
      }
    },
    
    // 获取项目键值
    getItemKey(item, index) {
      return item.id || item.key || index
    },
    
    // 获取项目样式类
    getItemClass(item, index) {
      return {
        'mobile-list-item-selected': this.isItemSelected(item),
        'mobile-list-item-even': index % 2 === 0,
        'mobile-list-item-odd': index % 2 === 1
      }
    },
    
    // 获取项目标题
    getItemTitle(item) {
      return item[this.titleField] || ''
    },
    
    // 获取项目描述
    getItemDescription(item) {
      return item[this.descriptionField] || ''
    },
    
    // 获取项目时间
    getItemTime(item) {
      return item[this.timeField] || ''
    },
    
    // 获取项目标签
    getItemTags(item) {
      const tags = item[this.tagsField]
      return Array.isArray(tags) ? tags : []
    },
    
    // 获取项目头像
    getItemAvatar(item) {
      return item[this.avatarField] || ''
    },
    
    // 获取头像颜色
    getAvatarColor(item) {
      const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae']
      const index = (item.id || 0) % colors.length
      return colors[index]
    },
    
    // 获取头像文本
    getAvatarText(item) {
      const title = this.getItemTitle(item)
      return title ? title.charAt(0).toUpperCase() : '?'
    },
    
    // 判断项目是否选中
    isItemSelected(item) {
      return this.selectedItems.some(selected => 
        (selected.id && selected.id === item.id) || selected === item
      )
    },
    
    // 处理项目点击
    handleItemClick(item, index) {
      this.$emit('itemClick', item, index)
    },
    
    // 处理项目选择
    handleItemSelect(item, event) {
      const checked = event.target.checked
      let newSelectedItems = [...this.selectedItems]
      
      if (checked) {
        if (!this.isItemSelected(item)) {
          newSelectedItems.push(item)
        }
      } else {
        newSelectedItems = newSelectedItems.filter(selected => 
          !(selected.id && selected.id === item.id) && selected !== item
        )
      }
      
      this.$emit('selectionChange', newSelectedItems)
    },
    
    // 处理操作点击
    handleActionClick(event, item, index) {
      this.$emit('actionClick', event.key, item, index)
    },
    
    // 处理搜索
    handleSearch(value) {
      this.searchValue = value
      this.$emit('search', value)
    },
    
    // 处理搜索变化
    handleSearchChange(event) {
      if (!event.target.value) {
        this.handleSearch('')
      }
    },
    
    // 处理刷新
    handleRefresh() {
      this.$emit('refresh')
    },
    
    // 处理分页变化
    handlePageChange(page, pageSize) {
      this.$emit('pageChange', page, pageSize)
    },
    
    // 处理页面大小变化
    handlePageSizeChange(current, size) {
      this.$emit('pageSizeChange', current, size)
    },
    
    // 滚动到顶部
    scrollToTop() {
      if (this.$refs.scrollContainer) {
        this.$refs.scrollContainer.scrollTop = 0
      }
    },
    
    // 滚动到指定项目
    scrollToItem(index) {
      if (this.$refs.scrollContainer) {
        const scrollTop = index * this.itemHeight
        this.$refs.scrollContainer.scrollTop = scrollTop
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/styles/mobile/index.less';

.virtual-mobile-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: @background-color-light;

  .mobile-list-header {
    .flex-between();
    align-items: center;
    padding: @spacing-md @spacing-lg;
    background-color: white;
    border-bottom: 1px solid @border-color-light;

    .mobile-list-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-medium;
      color: @text-color;
    }

    .mobile-list-actions {
      .d-flex();
      gap: @spacing-sm;
    }
  }

  .mobile-list-search {
    padding: @spacing-md @spacing-lg;
    background-color: white;
    border-bottom: 1px solid @border-color-light;
  }

  .virtual-scroll-container {
    flex: 1;
    overflow-y: auto;
    position: relative;
    -webkit-overflow-scrolling: touch;

    .virtual-scroll-placeholder {
      width: 100%;
      pointer-events: none;
    }

    .virtual-scroll-content {
      width: 100%;
    }

    .mobile-list-loading {
      .flex-center();
      padding: @spacing-xxl;
    }

    .mobile-list-empty {
      .flex-center();
      padding: @spacing-xxl;
    }

    .virtual-list-items {
      .mobile-list-item {
        .mobile-card();
        margin: @spacing-sm @spacing-md;
        padding: @spacing-md;
        .d-flex();
        align-items: center;
        gap: @spacing-md;
        cursor: pointer;
        .touch-feedback();

        &:hover {
          background-color: @background-color-light;
        }

        &.mobile-list-item-selected {
          background-color: @primary-color-light;
          border-color: @primary-color;
        }

        .mobile-item-avatar {
          flex-shrink: 0;
        }

        .mobile-item-content {
          flex: 1;
          min-width: 0;

          .mobile-item-title {
            .flex-between();
            align-items: flex-start;
            margin-bottom: @spacing-xs;

            .title-text {
              font-size: @font-size-base;
              font-weight: @font-weight-medium;
              color: @text-color;
              .text-ellipsis();
            }

            .title-time {
              font-size: @font-size-xs;
              color: @text-color-tertiary;
              flex-shrink: 0;
              margin-left: @spacing-sm;
            }
          }

          .mobile-item-description {
            font-size: @font-size-sm;
            color: @text-color-secondary;
            margin-bottom: @spacing-xs;
            .text-ellipsis-2();
          }

          .mobile-item-tags {
            .d-flex();
            flex-wrap: wrap;
            gap: @spacing-xs;

            .ant-tag {
              margin: 0;
            }
          }
        }

        .mobile-item-actions {
          flex-shrink: 0;
        }

        .mobile-item-selection {
          flex-shrink: 0;
          margin-left: @spacing-sm;
        }
      }
    }
  }

  .mobile-list-pagination {
    padding: @spacing-md @spacing-lg;
    background-color: white;
    border-top: 1px solid @border-color-light;
    text-align: center;
  }
}

// 移动端优化
@media (max-width: 768px) {
  .virtual-mobile-list {
    .virtual-scroll-container {
      .virtual-list-items {
        .mobile-list-item {
          margin: @spacing-xs @spacing-sm;
          padding: @spacing-sm;

          .mobile-item-content {
            .mobile-item-title {
              .title-text {
                font-size: @font-size-sm;
              }
            }

            .mobile-item-description {
              font-size: @font-size-xs;
            }
          }
        }
      }
    }
  }
}
</style>
