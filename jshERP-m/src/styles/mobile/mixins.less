/**
 * 移动端样式混入
 * 提供移动端优化的样式混入函数和工具类
 */

@import './variables.less';

// ==================== 响应式混入 ====================

// 媒体查询混入
.media-xs(@rules) {
  @media (max-width: @screen-xs-max) {
    @rules();
  }
}

.media-sm(@rules) {
  @media (min-width: @screen-sm) and (max-width: @screen-sm-max) {
    @rules();
  }
}

.media-md(@rules) {
  @media (min-width: @screen-md) and (max-width: @screen-md-max) {
    @rules();
  }
}

.media-lg(@rules) {
  @media (min-width: @screen-lg) and (max-width: @screen-lg-max) {
    @rules();
  }
}

.media-xl(@rules) {
  @media (min-width: @screen-xl) and (max-width: @screen-xl-max) {
    @rules();
  }
}

.media-xxl(@rules) {
  @media (min-width: @screen-xxl) {
    @rules();
  }
}

// 断点范围混入
.media-up(@breakpoint) when (@breakpoint = xs) {
  @media (min-width: @screen-xs) { @content(); }
}
.media-up(@breakpoint) when (@breakpoint = sm) {
  @media (min-width: @screen-sm) { @content(); }
}
.media-up(@breakpoint) when (@breakpoint = md) {
  @media (min-width: @screen-md) { @content(); }
}
.media-up(@breakpoint) when (@breakpoint = lg) {
  @media (min-width: @screen-lg) { @content(); }
}
.media-up(@breakpoint) when (@breakpoint = xl) {
  @media (min-width: @screen-xl) { @content(); }
}
.media-up(@breakpoint) when (@breakpoint = xxl) {
  @media (min-width: @screen-xxl) { @content(); }
}

.media-down(@breakpoint) when (@breakpoint = xs) {
  @media (max-width: @screen-xs-max) { @content(); }
}
.media-down(@breakpoint) when (@breakpoint = sm) {
  @media (max-width: @screen-sm-max) { @content(); }
}
.media-down(@breakpoint) when (@breakpoint = md) {
  @media (max-width: @screen-md-max) { @content(); }
}
.media-down(@breakpoint) when (@breakpoint = lg) {
  @media (max-width: @screen-lg-max) { @content(); }
}
.media-down(@breakpoint) when (@breakpoint = xl) {
  @media (max-width: @screen-xl-max) { @content(); }
}

// 移动端专用媒体查询
.mobile-only(@rules) {
  @media (max-width: @screen-md-max) {
    @rules();
  }
}

.tablet-only(@rules) {
  @media (min-width: @screen-md) and (max-width: @screen-lg-max) {
    @rules();
  }
}

.desktop-only(@rules) {
  @media (min-width: @screen-lg) {
    @rules();
  }
}

// ==================== 移动端优化混入 ====================

// 触摸优化
.touch-optimized() {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  touch-action: manipulation;
}

// 触摸反馈
.touch-feedback(@color: @mobile-touch-feedback-color, @duration: @mobile-touch-feedback-duration) {
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: @color;
    opacity: 0;
    transition: opacity @duration ease;
    pointer-events: none;
  }
  
  &:active::after {
    opacity: 1;
  }
}

// 安全区域适配
.safe-area-padding() {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}

.safe-area-margin() {
  margin-top: constant(safe-area-inset-top);
  margin-top: env(safe-area-inset-top);
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);
  margin-left: constant(safe-area-inset-left);
  margin-left: env(safe-area-inset-left);
  margin-right: constant(safe-area-inset-right);
  margin-right: env(safe-area-inset-right);
}

// 防止iOS缩放
.prevent-zoom() {
  font-size: 16px;
  
  .mobile-only({
    font-size: 16px !important;
  });
}

// ==================== 布局混入 ====================

// Flexbox 快捷方式
.flex-center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between() {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start() {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end() {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-column() {
  display: flex;
  flex-direction: column;
}

.flex-column-center() {
  .flex-column();
  align-items: center;
  justify-content: center;
}

// 网格布局
.grid(@columns: @grid-columns, @gap: @grid-gutter-width) {
  display: grid;
  grid-template-columns: repeat(@columns, 1fr);
  gap: @gap;
}

// 容器
.container(@max-width: none, @padding: @container-padding-mobile) {
  width: 100%;
  margin: 0 auto;
  padding-left: @padding;
  padding-right: @padding;
  
  & when not (@max-width = none) {
    max-width: @max-width;
  }
  
  .desktop-only({
    padding-left: @container-padding-desktop;
    padding-right: @container-padding-desktop;
  });
}

// ==================== 文本混入 ====================

// 文本省略
.text-ellipsis() {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-ellipsis-multiline(@lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 响应式字体大小
.responsive-font-size(@mobile-size, @desktop-size: @mobile-size) {
  font-size: @mobile-size;
  
  .desktop-only({
    font-size: @desktop-size;
  });
}

// ==================== 动画混入 ====================

// 过渡动画
.transition(@property: all, @duration: @animation-duration-base, @timing: @ease-base) {
  transition: @property @duration @timing;
}

// 移动端过渡动画（更快）
.mobile-transition(@property: all, @duration: @animation-duration-mobile-base, @timing: @ease-base) {
  transition: @property @duration @timing;
}

// 淡入动画
.fade-in(@duration: @animation-duration-base) {
  animation: fadeIn @duration @ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

// 滑入动画
.slide-in-up(@duration: @animation-duration-base) {
  animation: slideInUp @duration @ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 缩放动画
.scale-in(@duration: @animation-duration-base) {
  animation: scaleIn @duration @ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// ==================== 组件混入 ====================

// 按钮基础样式
.button-base() {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  cursor: pointer;
  user-select: none;
  text-decoration: none;
  outline: none;
  .transition();
  .touch-optimized();
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// 移动端按钮样式
.mobile-button() {
  .button-base();
  min-height: @btn-height-mobile;
  padding: 0 @btn-padding-horizontal-mobile;
  border-radius: @btn-border-radius-mobile;
  font-size: @font-size-base;
  .touch-feedback();
}

// 卡片基础样式
.card-base() {
  background: @background-color-base;
  border-radius: @border-radius-base;
  box-shadow: @box-shadow-sm;
  overflow: hidden;
}

// 移动端卡片样式
.mobile-card() {
  .card-base();
  padding: @card-padding-mobile;
  border-radius: @card-border-radius-mobile;
  box-shadow: @card-shadow-mobile;
}

// 输入框基础样式
.input-base() {
  display: block;
  width: 100%;
  border: 1px solid @border-color-base;
  background: @background-color-base;
  outline: none;
  .transition(border-color);
  
  &:focus {
    border-color: @primary-color;
  }
  
  &:disabled {
    background: @background-color-light;
    cursor: not-allowed;
  }
}

// 移动端输入框样式
.mobile-input() {
  .input-base();
  min-height: @input-height-mobile;
  padding: 0 @input-padding-horizontal-mobile;
  border-radius: @input-border-radius-mobile;
  font-size: @font-size-base;
  .prevent-zoom();
}

// ==================== 工具混入 ====================

// 清除浮动
.clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 隐藏滚动条
.hide-scrollbar() {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// 自定义滚动条
.custom-scrollbar(@width: 6px, @track-color: #f1f1f1, @thumb-color: #c1c1c1) {
  &::-webkit-scrollbar {
    width: @width;
  }
  
  &::-webkit-scrollbar-track {
    background: @track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: @thumb-color;
    border-radius: @width / 2;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: darken(@thumb-color, 10%);
  }
}

// 毛玻璃效果
.backdrop-blur(@blur: 10px, @opacity: 0.8) {
  backdrop-filter: blur(@blur);
  -webkit-backdrop-filter: blur(@blur);
  background-color: rgba(255, 255, 255, @opacity);
}
