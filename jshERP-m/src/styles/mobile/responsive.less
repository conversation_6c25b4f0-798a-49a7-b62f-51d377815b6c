/**
 * 响应式样式系统
 * 提供完整的响应式设计支持和断点管理
 */

@import './variables.less';
@import './mixins.less';

// ==================== 响应式容器 ====================

.responsive-container {
  width: 100%;
  margin: 0 auto;
  padding-left: @container-padding-mobile;
  padding-right: @container-padding-mobile;
  
  .media-sm({
    max-width: @container-max-width-sm;
    padding-left: @container-padding-tablet;
    padding-right: @container-padding-tablet;
  });
  
  @media (min-width: @screen-md-min) {
    max-width: @container-max-width-md;
  });
  
  .media-lg({
    max-width: @container-max-width-lg;
    padding-left: @container-padding-desktop;
    padding-right: @container-padding-desktop;
  });
  
  .media-xl({
    max-width: @container-max-width-xl;
  });
  
  .media-xxl({
    max-width: @container-max-width-xxl;
  });
}

// ==================== 响应式网格 ====================

.responsive-grid {
  display: grid;
  gap: @grid-gutter-width;
  
  // 移动端：单列布局
  grid-template-columns: 1fr;
  
  // 平板端：双列布局
  @media (min-width: @screen-md-min) {
    grid-template-columns: repeat(2, 1fr);
    gap: @grid-gutter-width-desktop;
  });
  
  // 桌面端：多列布局
  .media-lg({
    grid-template-columns: repeat(3, 1fr);
  });
  
  .media-xl({
    grid-template-columns: repeat(4, 1fr);
  });
}

// 响应式网格项
.responsive-grid-item {
  &.span-full {
    grid-column: 1 / -1;
  }
  
  &.span-half {
    @media (min-width: @screen-md-min) {
      grid-column: span 1;
    });
  }
  
  &.span-two-thirds {
    .media-lg({
      grid-column: span 2;
    });
  }
  
  &.span-three-quarters {
    .media-xl({
      grid-column: span 3;
    });
  }
}

// ==================== 响应式Flexbox ====================

.responsive-flex {
  display: flex;
  flex-direction: column;
  gap: @spacing-md;
  
  @media (min-width: @screen-md-min) {
    flex-direction: row;
    gap: @spacing-lg;
  });
  
  &.flex-wrap {
    flex-wrap: wrap;
  }
  
  &.flex-center {
    align-items: center;
    justify-content: center;
  }
  
  &.flex-between {
    @media (min-width: @screen-md-min) {
      justify-content: space-between;
    });
  }
  
  &.flex-around {
    @media (min-width: @screen-md-min) {
      justify-content: space-around;
    });
  }
}

.responsive-flex-item {
  flex: 1;
  
  &.flex-auto {
    flex: 0 0 auto;
  }
  
  &.flex-grow {
    flex-grow: 1;
  }
  
  &.flex-shrink {
    flex-shrink: 1;
  }
  
  &.flex-none {
    flex: none;
  }
}

// ==================== 响应式间距 ====================

// 生成响应式间距类
.generate-responsive-spacing(@prefix, @property) {
  .@{prefix}-xs {
    @{property}: @spacing-xs;
    
    @media (min-width: @screen-md-min) {
      @{property}: @spacing-desktop-xs;
    });
  }
  
  .@{prefix}-sm {
    @{property}: @spacing-sm;
    
    @media (min-width: @screen-md-min) {
      @{property}: @spacing-desktop-sm;
    });
  }
  
  .@{prefix}-md {
    @{property}: @spacing-md;
    
    @media (min-width: @screen-md-min) {
      @{property}: @spacing-desktop-md;
    });
  }
  
  .@{prefix}-lg {
    @{property}: @spacing-lg;
    
    @media (min-width: @screen-md-min) {
      @{property}: @spacing-desktop-lg;
    });
  }
  
  .@{prefix}-xl {
    @{property}: @spacing-xl;
    
    @media (min-width: @screen-md-min) {
      @{property}: @spacing-desktop-xl;
    });
  }
  
  .@{prefix}-xxl {
    @{property}: @spacing-xxl;
    
    @media (min-width: @screen-md-min) {
      @{property}: @spacing-desktop-xxl;
    });
  }
}

// 生成响应式间距类
.generate-responsive-spacing(r-m, margin);
.generate-responsive-spacing(r-p, padding);
.generate-responsive-spacing(r-mt, margin-top);
.generate-responsive-spacing(r-mr, margin-right);
.generate-responsive-spacing(r-mb, margin-bottom);
.generate-responsive-spacing(r-ml, margin-left);
.generate-responsive-spacing(r-pt, padding-top);
.generate-responsive-spacing(r-pr, padding-right);
.generate-responsive-spacing(r-pb, padding-bottom);
.generate-responsive-spacing(r-pl, padding-left);

// ==================== 响应式字体 ====================

.responsive-text {
  &.text-xs {
    .responsive-font-size(@font-size-xs, @font-size-desktop-xs);
  }
  
  &.text-sm {
    .responsive-font-size(@font-size-sm, @font-size-desktop-sm);
  }
  
  &.text-base {
    .responsive-font-size(@font-size-base, @font-size-desktop-base);
  }
  
  &.text-lg {
    .responsive-font-size(@font-size-lg, @font-size-desktop-lg);
  }
  
  &.text-xl {
    .responsive-font-size(@font-size-xl, @font-size-desktop-xl);
  }
  
  &.text-xxl {
    .responsive-font-size(@font-size-xxl, @font-size-desktop-xxl);
  }
}

// ==================== 响应式显示/隐藏 ====================

// 断点特定显示
.show-xs {
  display: block;
  
  .media-sm({
    display: none;
  });
}

.show-sm {
  display: none;
  
  .media-sm({
    display: block;
  });
  
  @media (min-width: @screen-md-min) {
    display: none;
  });
}

.show-md {
  display: none;
  
  @media (min-width: @screen-md-min) {
    display: block;
  });
  
  .media-lg({
    display: none;
  });
}

.show-lg {
  display: none;
  
  .media-lg({
    display: block;
  });
  
  .media-xl({
    display: none;
  });
}

.show-xl {
  display: none;
  
  .media-xl({
    display: block;
  });
  
  .media-xxl({
    display: none;
  });
}

.show-xxl {
  display: none;
  
  .media-xxl({
    display: block;
  });
}

// 断点范围显示
.show-xs-up {
  display: block;
}

.show-sm-up {
  display: none;
  
  .media-sm({
    display: block;
  });
}

.show-md-up {
  display: none;
  
  @media (min-width: @screen-md-min) {
    display: block;
  });
}

.show-lg-up {
  display: none;
  
  .media-lg({
    display: block;
  });
}

.show-xl-up {
  display: none;
  
  .media-xl({
    display: block;
  });
}

.show-xs-down {
  display: block;
  
  .media-sm({
    display: none;
  });
}

.show-sm-down {
  display: block;
  
  @media (min-width: @screen-md-min) {
    display: none;
  });
}

.show-md-down {
  display: block;
  
  .media-lg({
    display: none;
  });
}

.show-lg-down {
  display: block;
  
  .media-xl({
    display: none;
  });
}

.show-xl-down {
  display: block;
  
  .media-xxl({
    display: none;
  });
}

// ==================== 响应式布局组件 ====================

.responsive-card {
  .mobile-card();
  margin-bottom: @spacing-md;
  
  @media (min-width: @screen-md-min) {
    margin-bottom: @spacing-lg;
  });
  
  .responsive-card-header {
    padding: @spacing-md @spacing-lg;
    border-bottom: 1px solid @border-color-light;
    
    @media (min-width: @screen-md-min) {
      padding: @spacing-lg @spacing-xl;
    });
    
    .responsive-card-title {
      .responsive-font-size(@font-size-lg, @font-size-desktop-lg);
      font-weight: @font-weight-semibold;
      margin: 0;
    }
  }
  
  .responsive-card-body {
    padding: @spacing-lg;
    
    @media (min-width: @screen-md-min) {
      padding: @spacing-xl;
    });
  }
  
  .responsive-card-footer {
    padding: @spacing-md @spacing-lg;
    border-top: 1px solid @border-color-light;
    background-color: @background-color-light;
    
    @media (min-width: @screen-md-min) {
      padding: @spacing-lg @spacing-xl;
    });
  }
}

.responsive-section {
  padding: @spacing-lg 0;
  
  @media (min-width: @screen-md-min) {
    padding: @spacing-xl 0;
  });
  
  .media-lg({
    padding: @spacing-xxl 0;
  });
  
  &.section-compact {
    padding: @spacing-md 0;
    
    @media (min-width: @screen-md-min) {
      padding: @spacing-lg 0;
    });
  }
  
  &.section-spacious {
    padding: @spacing-xl 0;
    
    @media (min-width: @screen-md-min) {
      padding: @spacing-xxl 0;
    });
    
    .media-lg({
      padding: (@spacing-xxl * 1.5) 0;
    });
  }
}

// ==================== 响应式表格 ====================

.responsive-table {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  
  @media (max-width: @screen-md-max) {
    .hide-scrollbar();

    table {
      min-width: 600px;
    }
    
    th, td {
      padding: @spacing-sm;
      font-size: @font-size-sm;
      white-space: nowrap;
    }
  }
  
  @media (min-width: @screen-md-min) {
    overflow-x: visible;
    
    table {
      min-width: auto;
    }
    
    th, td {
      padding: @spacing-md;
      font-size: @font-size-base;
      white-space: normal;
    }
  });
}

// ==================== 响应式导航 ====================

.responsive-nav {
  @media (max-width: @screen-md-max) {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: @background-color-base;
    border-top: 1px solid @border-color-light;
    padding-bottom: @safe-area-inset-bottom;
    z-index: @zindex-fixed;
  });
  
  @media (min-width: @screen-md-min) {
    position: static;
    background: transparent;
    border: none;
    padding: 0;
  });
}

// ==================== 响应式模态框 ====================

.responsive-modal {
  @media (max-width: @screen-md-max) {
    .ant-modal {
      margin: 0;
      max-width: none;
      width: 100% !important;
      height: 100%;
      
      .ant-modal-content {
        height: 100%;
        border-radius: 0;
        display: flex;
        flex-direction: column;
      }
      
      .ant-modal-body {
        flex: 1;
        overflow-y: auto;
      }
    }
  });
  
  @media (min-width: @screen-md-min) {
    .ant-modal {
      margin: 48px auto;
      max-width: 520px;
      width: auto !important;
      height: auto;
      
      .ant-modal-content {
        height: auto;
        border-radius: @border-radius-lg;
        display: block;
      }
      
      .ant-modal-body {
        flex: none;
        overflow-y: visible;
      }
    }
  });
}
