/**
 * 移动端基础样式
 * 包含样式重置、基础元素样式和移动端优化
 */

@import './variables.less';
@import './mixins.less';

// ==================== 样式重置 ====================

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

*::before,
*::after {
  box-sizing: border-box;
}

// ==================== 根元素 ====================

html {
  font-family: @font-family-base;
  font-size: @font-size-base;
  line-height: @line-height-base;
  color: @text-color;
  background-color: @background-color-light;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  // 移动端优化
  @media (max-width: @screen-md-max) {
    font-size: @font-size-base;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
  }
  
  // 桌面端字体大小
  .desktop-only({
    font-size: @font-size-desktop-base;
  });
}

body {
  margin: 0;
  padding: 0;
  background-color: @background-color-light;
  color: @text-color;
  font-family: @font-family-base;
  font-size: @font-size-base;
  line-height: @line-height-base;
  overflow-x: hidden;
  
  // 移动端优化
  @media (max-width: @screen-md-max) {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
  }
}

// ==================== 文本元素 ====================

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: @font-weight-semibold;
  line-height: @line-height-tight;
  color: @text-color;
}

h1 {
  .responsive-font-size(@font-size-xxl, @font-size-desktop-xxl);
  margin-bottom: @spacing-lg;
}

h2 {
  .responsive-font-size(@font-size-xl, @font-size-desktop-xl);
  margin-bottom: @spacing-md;
}

h3 {
  .responsive-font-size(@font-size-lg, @font-size-desktop-lg);
  margin-bottom: @spacing-md;
}

h4 {
  .responsive-font-size(@font-size-base, @font-size-desktop-base);
  margin-bottom: @spacing-sm;
}

h5 {
  .responsive-font-size(@font-size-sm, @font-size-desktop-sm);
  margin-bottom: @spacing-sm;
}

h6 {
  .responsive-font-size(@font-size-xs, @font-size-desktop-xs);
  margin-bottom: @spacing-xs;
}

p {
  margin: 0 0 @spacing-md 0;
  line-height: @line-height-base;
}

a {
  color: @primary-color;
  text-decoration: none;
  .transition(color);
  
  &:hover {
    color: @primary-color-hover;
  }
  
  &:active {
    color: @primary-color-active;
  }
  
  // 移动端触摸优化
  @media (max-width: @screen-md-max) {
    .touch-optimized();
  }
}

// ==================== 列表元素 ====================

ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

li {
  margin: 0;
  padding: 0;
}

// ==================== 表单元素 ====================

input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  margin: 0;
  
  // 移动端优化
  @media (max-width: @screen-md-max) {
    .prevent-zoom();
  }
}

button {
  background: none;
  border: none;
  cursor: pointer;
  outline: none;
  .touch-optimized();
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

input, textarea {
  outline: none;
  border: none;
  background: transparent;
  
  &::placeholder {
    color: @text-color-tertiary;
  }
}

// ==================== 图片和媒体 ====================

img {
  max-width: 100%;
  height: auto;
  border: none;
  vertical-align: middle;
}

svg {
  vertical-align: middle;
}

// ==================== 表格 ====================

table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

th, td {
  text-align: left;
  vertical-align: top;
  padding: @spacing-sm;
  border-bottom: 1px solid @border-color-light;
}

th {
  font-weight: @font-weight-semibold;
  background-color: @background-color-dark;
}

// ==================== 移动端专用样式 ====================

// 安全区域适配
.safe-area {
  .safe-area-padding();
}

.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}

// 移动端容器
.mobile-container {
  .container();
  
  @media (max-width: @screen-md-max) {
    padding-left: @container-padding-mobile;
    padding-right: @container-padding-mobile;
  }
  
  .tablet-only({
    padding-left: @container-padding-tablet;
    padding-right: @container-padding-tablet;
  });
  
  .desktop-only({
    padding-left: @container-padding-desktop;
    padding-right: @container-padding-desktop;
  });
}

// 移动端页面
.mobile-page {
  min-height: 100vh;
  background-color: @background-color-light;
  
  @media (max-width: @screen-md-max) {
    padding-bottom: @safe-area-inset-bottom;
  }
}

// 移动端内容区域
.mobile-content {
  padding: @spacing-lg;
  
  @media (max-width: @screen-md-max) {
    padding: @spacing-md;
  }
}

// ==================== 工具类 ====================

// 显示/隐藏
.show {
  display: block !important;
}

.hide {
  display: none !important;
}

.invisible {
  visibility: hidden !important;
}

// 响应式显示/隐藏
.mobile-show {
  .desktop-only({
    display: none !important;
  });
}

.mobile-hide {
  @media (max-width: @screen-md-max) {
    display: none !important;
  }
}

.desktop-show {
  @media (max-width: @screen-md-max) {
    display: none !important;
  }
}

.desktop-hide {
  .desktop-only({
    display: none !important;
  });
}

// 文本对齐
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

// 文本颜色
.text-primary { color: @primary-color !important; }
.text-success { color: @success-color !important; }
.text-warning { color: @warning-color !important; }
.text-error { color: @error-color !important; }
.text-secondary { color: @text-color-secondary !important; }
.text-tertiary { color: @text-color-tertiary !important; }
.text-disabled { color: @text-color-disabled !important; }

// 背景颜色
.bg-primary { background-color: @primary-color !important; }
.bg-success { background-color: @success-color !important; }
.bg-warning { background-color: @warning-color !important; }
.bg-error { background-color: @error-color !important; }
.bg-light { background-color: @background-color-light !important; }
.bg-white { background-color: @background-color-base !important; }

// 间距工具类
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mr-0 { margin-right: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }

.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pr-0 { padding-right: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }

// 生成间距工具类
.generate-spacing-utilities(@prefix, @property, @sizes) {
  .loop(@i: 1) when (@i <= length(@sizes)) {
    @size: extract(@sizes, @i);
    @value: @size * @spacing-unit;
    
    .@{prefix}-@{i} {
      @{property}: @value !important;
    }
    
    .@{prefix}t-@{i} {
      @{property}-top: @value !important;
    }
    
    .@{prefix}r-@{i} {
      @{property}-right: @value !important;
    }
    
    .@{prefix}b-@{i} {
      @{property}-bottom: @value !important;
    }
    
    .@{prefix}l-@{i} {
      @{property}-left: @value !important;
    }
    
    .@{prefix}x-@{i} {
      @{property}-left: @value !important;
      @{property}-right: @value !important;
    }
    
    .@{prefix}y-@{i} {
      @{property}-top: @value !important;
      @{property}-bottom: @value !important;
    }
    
    .loop(@i + 1);
  }
}

// 生成 margin 和 padding 工具类
.generate-spacing-utilities(m, margin, 1 2 3 4 5 6 8 10 12 16 20 24);
.generate-spacing-utilities(p, padding, 1 2 3 4 5 6 8 10 12 16 20 24);

// Flexbox 工具类
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }
.align-stretch { align-items: stretch !important; }

// 边框工具类
.border { border: 1px solid @border-color-base !important; }
.border-top { border-top: 1px solid @border-color-base !important; }
.border-right { border-right: 1px solid @border-color-base !important; }
.border-bottom { border-bottom: 1px solid @border-color-base !important; }
.border-left { border-left: 1px solid @border-color-base !important; }
.border-0 { border: none !important; }

// 圆角工具类
.rounded { border-radius: @border-radius-base !important; }
.rounded-sm { border-radius: @border-radius-sm !important; }
.rounded-lg { border-radius: @border-radius-lg !important; }
.rounded-xl { border-radius: @border-radius-xl !important; }
.rounded-full { border-radius: @border-radius-full !important; }

// 阴影工具类
.shadow { box-shadow: @box-shadow-base !important; }
.shadow-sm { box-shadow: @box-shadow-sm !important; }
.shadow-lg { box-shadow: @box-shadow-lg !important; }
.shadow-none { box-shadow: none !important; }
