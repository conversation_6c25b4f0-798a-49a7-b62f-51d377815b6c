/**
 * 移动端组件样式
 * 针对Ant Design Vue组件的移动端优化样式
 */

@import './variables.less';
@import './mixins.less';

// ==================== 按钮组件 ====================

.ant-btn {
  .mobile-only({
    .mobile-button();
    
    &.ant-btn-sm {
      min-height: @height-base;
      padding: 0 @spacing-md;
      font-size: @font-size-sm;
    }
    
    &.ant-btn-lg {
      min-height: @height-xl;
      padding: 0 @spacing-xl;
      font-size: @font-size-lg;
    }
    
    &.ant-btn-block {
      width: 100%;
      margin-bottom: @spacing-sm;
    }
    
    // 主要按钮
    &.ant-btn-primary {
      background-color: @primary-color;
      border-color: @primary-color;
      color: #fff;
      
      &:hover, &:focus {
        background-color: @primary-color-hover;
        border-color: @primary-color-hover;
      }
      
      &:active {
        background-color: @primary-color-active;
        border-color: @primary-color-active;
      }
    }
    
    // 危险按钮
    &.ant-btn-dangerous {
      border-color: @error-color;
      color: @error-color;
      
      &.ant-btn-primary {
        background-color: @error-color;
        color: #fff;
      }
    }
  });
}

// ==================== 输入框组件 ====================

.ant-input {
  .mobile-only({
    .mobile-input();
    
    &.ant-input-sm {
      min-height: @height-base;
      padding: 0 @spacing-sm;
      font-size: @font-size-sm;
    }
    
    &.ant-input-lg {
      min-height: @height-xl;
      padding: 0 @spacing-lg;
      font-size: @font-size-lg;
    }
  });
}

.ant-input-group {
  .mobile-only({
    .ant-input-group-addon {
      padding: 0 @spacing-md;
      background-color: @background-color-dark;
      border-color: @border-color-base;
    }
  });
}

// 文本域
.ant-input {
  &[type="textarea"] {
    .mobile-only({
      min-height: 80px;
      padding: @spacing-sm @spacing-md;
      line-height: @line-height-base;
      resize: vertical;
    });
  }
}

// ==================== 选择器组件 ====================

.ant-select {
  .mobile-only({
    .ant-select-selector {
      .mobile-input();
      
      .ant-select-selection-search-input {
        height: auto;
      }
      
      .ant-select-selection-item {
        line-height: @height-lg - 2px;
      }
    }
    
    &.ant-select-sm .ant-select-selector {
      min-height: @height-base;
    }
    
    &.ant-select-lg .ant-select-selector {
      min-height: @height-xl;
    }
  });
}

// 下拉面板
.ant-select-dropdown {
  .mobile-only({
    border-radius: @border-radius-mobile-lg;
    box-shadow: @box-shadow-mobile-lg;
    
    .ant-select-item {
      padding: @spacing-md @spacing-lg;
      min-height: @touch-target-min;
      line-height: 1.2;
      
      &.ant-select-item-option-selected {
        background-color: @primary-color-light;
      }
      
      &.ant-select-item-option-active {
        background-color: @mobile-active-background;
      }
    }
  });
}

// ==================== 表单组件 ====================

.ant-form {
  .mobile-only({
    .ant-form-item {
      margin-bottom: @spacing-lg;
      
      .ant-form-item-label {
        padding-bottom: @spacing-xs;
        
        > label {
          font-size: @font-size-base;
          font-weight: @font-weight-medium;
          color: @text-color;
        }
      }
      
      .ant-form-item-control {
        .ant-form-item-explain {
          font-size: @font-size-sm;
          margin-top: @spacing-xs;
        }
      }
    }
    
    &.ant-form-vertical {
      .ant-form-item-label {
        text-align: left;
      }
    }
  });
}

// ==================== 表格组件 ====================

.ant-table {
  .mobile-only({
    font-size: @font-size-sm;
    
    .ant-table-thead > tr > th {
      padding: @spacing-sm;
      font-size: @font-size-sm;
      font-weight: @font-weight-semibold;
      background-color: @background-color-dark;
      border-bottom: 1px solid @border-color-base;
    }
    
    .ant-table-tbody > tr > td {
      padding: @spacing-sm;
      border-bottom: 1px solid @border-color-light;
    }
    
    .ant-table-row {
      &:hover {
        background-color: @mobile-active-background;
      }
    }
    
    // 表格分页
    .ant-pagination {
      margin-top: @spacing-lg;
      text-align: center;
      
      .ant-pagination-item {
        min-width: @touch-target-min;
        height: @touch-target-min;
        line-height: @touch-target-min - 2px;
        margin-right: @spacing-xs;
      }
      
      .ant-pagination-prev,
      .ant-pagination-next {
        min-width: @touch-target-min;
        height: @touch-target-min;
        line-height: @touch-target-min - 2px;
      }
    }
  });
}

// ==================== 卡片组件 ====================

.ant-card {
  .mobile-only({
    .mobile-card();
    
    .ant-card-head {
      padding: @spacing-md @spacing-lg;
      border-bottom: 1px solid @border-color-light;
      
      .ant-card-head-title {
        font-size: @font-size-lg;
        font-weight: @font-weight-semibold;
      }
    }
    
    .ant-card-body {
      padding: @spacing-lg;
    }
    
    .ant-card-actions {
      background-color: @background-color-light;
      border-top: 1px solid @border-color-light;
      
      > li {
        margin: @spacing-sm 0;
        
        > span {
          padding: @spacing-sm @spacing-md;
          min-height: @touch-target-min;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  });
}

// ==================== 模态框组件 ====================

.ant-modal {
  .mobile-only({
    .ant-modal-content {
      border-radius: @modal-border-radius-mobile;
      overflow: hidden;
    }
    
    .ant-modal-header {
      padding: @spacing-lg;
      border-bottom: 1px solid @border-color-light;
      
      .ant-modal-title {
        font-size: @font-size-lg;
        font-weight: @font-weight-semibold;
      }
    }
    
    .ant-modal-body {
      padding: @modal-padding-mobile;
      max-height: 60vh;
      overflow-y: auto;
    }
    
    .ant-modal-footer {
      padding: @spacing-md @spacing-lg @spacing-lg;
      border-top: 1px solid @border-color-light;
      text-align: center;
      
      .ant-btn {
        margin: 0 @spacing-xs;
        min-width: 80px;
        
        &:first-child {
          margin-left: 0;
        }
        
        &:last-child {
          margin-right: 0;
        }
      }
    }
    
    .ant-modal-close {
      top: @spacing-md;
      right: @spacing-md;
      width: @touch-target-min;
      height: @touch-target-min;
      line-height: @touch-target-min;
    }
  });
}

// ==================== 抽屉组件 ====================

.ant-drawer {
  .mobile-only({
    .ant-drawer-content {
      border-radius: @drawer-border-radius-mobile @drawer-border-radius-mobile 0 0;
    }
    
    .ant-drawer-header {
      padding: @spacing-lg;
      border-bottom: 1px solid @border-color-light;
      
      .ant-drawer-title {
        font-size: @font-size-lg;
        font-weight: @font-weight-semibold;
      }
    }
    
    .ant-drawer-body {
      padding: @drawer-padding-mobile;
    }
    
    .ant-drawer-close {
      top: @spacing-md;
      right: @spacing-md;
      width: @touch-target-min;
      height: @touch-target-min;
      line-height: @touch-target-min;
    }
  });
}

// ==================== 消息组件 ====================

.ant-message {
  .mobile-only({
    top: @safe-area-inset-top + @spacing-lg;
    
    .ant-message-notice {
      .ant-message-notice-content {
        padding: @spacing-md @spacing-lg;
        border-radius: @border-radius-mobile-lg;
        box-shadow: @box-shadow-mobile-base;
        font-size: @font-size-base;
      }
    }
  });
}

// ==================== 通知组件 ====================

.ant-notification {
  .mobile-only({
    top: @safe-area-inset-top + @spacing-lg;
    right: @spacing-md;
    width: calc(100% - @spacing-md * 2);
    
    .ant-notification-notice {
      margin-bottom: @spacing-md;
      border-radius: @border-radius-mobile-lg;
      box-shadow: @box-shadow-mobile-base;
      
      .ant-notification-notice-content {
        padding: @spacing-lg;
        
        .ant-notification-notice-message {
          font-size: @font-size-base;
          font-weight: @font-weight-semibold;
          margin-bottom: @spacing-xs;
        }
        
        .ant-notification-notice-description {
          font-size: @font-size-sm;
          line-height: @line-height-base;
        }
      }
      
      .ant-notification-notice-close {
        top: @spacing-md;
        right: @spacing-md;
        width: @touch-target-min;
        height: @touch-target-min;
        line-height: @touch-target-min;
      }
    }
  });
}

// ==================== 标签页组件 ====================

.ant-tabs {
  .mobile-only({
    .ant-tabs-nav {
      margin-bottom: @spacing-md;
      
      .ant-tabs-tab {
        padding: @spacing-md @spacing-lg;
        font-size: @font-size-base;
        min-height: @touch-target-min;
        
        &.ant-tabs-tab-active {
          font-weight: @font-weight-semibold;
        }
      }
    }
    
    .ant-tabs-content {
      padding: 0 @spacing-md;
    }
  });
}

// ==================== 步骤条组件 ====================

.ant-steps {
  .mobile-only({
    .ant-steps-item {
      .ant-steps-item-icon {
        width: @touch-target-min;
        height: @touch-target-min;
        line-height: @touch-target-min;
      }
      
      .ant-steps-item-content {
        margin-top: @spacing-xs;
        
        .ant-steps-item-title {
          font-size: @font-size-base;
          line-height: @line-height-base;
        }
        
        .ant-steps-item-description {
          font-size: @font-size-sm;
          margin-top: @spacing-xs;
        }
      }
    }
  });
}

// ==================== 加载组件 ====================

.ant-spin {
  .mobile-only({
    .ant-spin-dot {
      font-size: @font-size-lg;
    }
    
    .ant-spin-text {
      font-size: @font-size-base;
      margin-top: @spacing-md;
    }
  });
}
