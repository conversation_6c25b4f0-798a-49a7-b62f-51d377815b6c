/**
 * 测试数据生成器
 * 用于生成各种测试场景的数据
 */

/**
 * 生成随机字符串
 */
function generateRandomString(length = 10) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成随机数字
 */
function generateRandomNumber(min = 0, max = 1000) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 生成随机日期
 */
function generateRandomDate(startDate = new Date(2020, 0, 1), endDate = new Date()) {
  const start = startDate.getTime()
  const end = endDate.getTime()
  return new Date(start + Math.random() * (end - start))
}

/**
 * 生成商品测试数据
 */
export function generateMaterialData(count = 1000) {
  const categories = ['电子产品', '服装鞋帽', '家居用品', '食品饮料', '图书文具', '运动户外', '美妆护肤', '母婴用品']
  const units = ['个', '件', '套', '盒', '包', '瓶', '袋', '台']
  const brands = ['品牌A', '品牌B', '品牌C', '品牌D', '品牌E', '品牌F', '品牌G', '品牌H']
  
  const materials = []
  
  for (let i = 0; i < count; i++) {
    const category = categories[Math.floor(Math.random() * categories.length)]
    const brand = brands[Math.floor(Math.random() * brands.length)]
    const unit = units[Math.floor(Math.random() * units.length)]
    
    const currentStock = generateRandomNumber(0, 1000)
    const lowSafeStock = generateRandomNumber(10, 50)
    
    materials.push({
      id: `material_${i + 1}`,
      name: `${category}_${brand}_${generateRandomString(6)}`,
      code: `M${String(i + 1).padStart(6, '0')}`,
      barcode: generateRandomString(13),
      categoryId: `cat_${Math.floor(Math.random() * 10) + 1}`,
      categoryName: category,
      brand: brand,
      unit: unit,
      purchasePrice: (generateRandomNumber(100, 10000) / 100).toFixed(2),
      salePrice: (generateRandomNumber(150, 15000) / 100).toFixed(2),
      currentStock: currentStock,
      lowSafeStock: lowSafeStock,
      enabled: Math.random() > 0.1 ? '1' : '0', // 90%启用
      isSerialNumber: Math.random() > 0.8 ? '1' : '0', // 20%序列号管理
      description: `这是${category}类别下的${brand}品牌商品，编号${i + 1}`,
      createTime: generateRandomDate(),
      updateTime: generateRandomDate(),
      
      // 计算字段
      updateTimeText: '',
      tags: [],
      stockInfo: '',
      hasStock: currentStock > 0
    })
  }
  
  return materials
}

/**
 * 生成订单测试数据
 */
export function generateOrderData(count = 500) {
  const orderTypes = ['销售订单', '采购订单', '退货订单', '调拨订单']
  const statuses = ['待审核', '已审核', '部分出库', '已完成', '已取消']
  const customers = ['客户A', '客户B', '客户C', '客户D', '客户E']
  
  const orders = []
  
  for (let i = 0; i < count; i++) {
    const orderType = orderTypes[Math.floor(Math.random() * orderTypes.length)]
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const customer = customers[Math.floor(Math.random() * customers.length)]
    
    orders.push({
      id: `order_${i + 1}`,
      orderNumber: `SO${new Date().getFullYear()}${String(i + 1).padStart(6, '0')}`,
      orderType: orderType,
      status: status,
      customerId: `customer_${Math.floor(Math.random() * 20) + 1}`,
      customerName: customer,
      totalAmount: (generateRandomNumber(10000, 1000000) / 100).toFixed(2),
      paidAmount: (generateRandomNumber(0, 100000) / 100).toFixed(2),
      orderDate: generateRandomDate(),
      deliveryDate: generateRandomDate(new Date(), new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
      remark: `订单备注信息${i + 1}`,
      createTime: generateRandomDate(),
      updateTime: generateRandomDate(),
      
      // 计算字段
      updateTimeText: '',
      tags: [],
      statusColor: getStatusColor(status)
    })
  }
  
  return orders
}

/**
 * 生成库存测试数据
 */
export function generateInventoryData(count = 800) {
  const warehouses = ['主仓库', '分仓库A', '分仓库B', '临时仓库']
  const operations = ['入库', '出库', '调拨', '盘点']
  
  const inventory = []
  
  for (let i = 0; i < count; i++) {
    const warehouse = warehouses[Math.floor(Math.random() * warehouses.length)]
    const operation = operations[Math.floor(Math.random() * operations.length)]
    
    inventory.push({
      id: `inventory_${i + 1}`,
      materialId: `material_${generateRandomNumber(1, 1000)}`,
      materialName: `商品${generateRandomNumber(1, 1000)}`,
      materialCode: `M${String(generateRandomNumber(1, 1000)).padStart(6, '0')}`,
      warehouseId: `warehouse_${Math.floor(Math.random() * 4) + 1}`,
      warehouseName: warehouse,
      operation: operation,
      quantity: generateRandomNumber(1, 100),
      unitPrice: (generateRandomNumber(100, 5000) / 100).toFixed(2),
      totalAmount: 0, // 将在后面计算
      operationDate: generateRandomDate(),
      operator: `操作员${Math.floor(Math.random() * 10) + 1}`,
      remark: `${operation}操作备注${i + 1}`,
      createTime: generateRandomDate(),
      updateTime: generateRandomDate(),
      
      // 计算字段
      updateTimeText: '',
      tags: [],
      operationColor: getOperationColor(operation)
    })
    
    // 计算总金额
    inventory[i].totalAmount = (inventory[i].quantity * parseFloat(inventory[i].unitPrice)).toFixed(2)
  }
  
  return inventory
}

/**
 * 生成客户测试数据
 */
export function generateCustomerData(count = 200) {
  const customerTypes = ['个人客户', '企业客户', 'VIP客户', '代理商']
  const levels = ['普通', '银牌', '金牌', '钻石']
  const cities = ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉']
  
  const customers = []
  
  for (let i = 0; i < count; i++) {
    const customerType = customerTypes[Math.floor(Math.random() * customerTypes.length)]
    const level = levels[Math.floor(Math.random() * levels.length)]
    const city = cities[Math.floor(Math.random() * cities.length)]
    
    customers.push({
      id: `customer_${i + 1}`,
      name: `${customerType}_${generateRandomString(4)}`,
      code: `C${String(i + 1).padStart(6, '0')}`,
      type: customerType,
      level: level,
      phone: `1${generateRandomNumber(3, 9)}${String(generateRandomNumber(0, 999999999)).padStart(9, '0')}`,
      email: `customer${i + 1}@example.com`,
      address: `${city}市${generateRandomString(6)}路${generateRandomNumber(1, 999)}号`,
      contactPerson: `联系人${i + 1}`,
      creditLimit: (generateRandomNumber(10000, 1000000) / 100).toFixed(2),
      balance: (generateRandomNumber(0, 100000) / 100).toFixed(2),
      enabled: Math.random() > 0.05 ? '1' : '0', // 95%启用
      createTime: generateRandomDate(),
      updateTime: generateRandomDate(),
      
      // 计算字段
      updateTimeText: '',
      tags: [],
      levelColor: getLevelColor(level)
    })
  }
  
  return customers
}

/**
 * 生成性能测试数据集
 */
export function generatePerformanceTestData() {
  return {
    // 小数据集（测试普通列表）
    small: {
      materials: generateMaterialData(50),
      orders: generateOrderData(30),
      inventory: generateInventoryData(40),
      customers: generateCustomerData(20)
    },
    
    // 中等数据集（测试虚拟滚动临界点）
    medium: {
      materials: generateMaterialData(150),
      orders: generateOrderData(100),
      inventory: generateInventoryData(120),
      customers: generateCustomerData(80)
    },
    
    // 大数据集（测试虚拟滚动性能）
    large: {
      materials: generateMaterialData(1000),
      orders: generateOrderData(800),
      inventory: generateInventoryData(1200),
      customers: generateCustomerData(500)
    },
    
    // 超大数据集（压力测试）
    xlarge: {
      materials: generateMaterialData(5000),
      orders: generateOrderData(3000),
      inventory: generateInventoryData(4000),
      customers: generateCustomerData(2000)
    }
  }
}

/**
 * 获取状态颜色
 */
function getStatusColor(status) {
  const colorMap = {
    '待审核': 'orange',
    '已审核': 'blue',
    '部分出库': 'cyan',
    '已完成': 'green',
    '已取消': 'red'
  }
  return colorMap[status] || 'default'
}

/**
 * 获取操作颜色
 */
function getOperationColor(operation) {
  const colorMap = {
    '入库': 'green',
    '出库': 'red',
    '调拨': 'blue',
    '盘点': 'orange'
  }
  return colorMap[operation] || 'default'
}

/**
 * 获取等级颜色
 */
function getLevelColor(level) {
  const colorMap = {
    '普通': 'default',
    '银牌': 'cyan',
    '金牌': 'gold',
    '钻石': 'purple'
  }
  return colorMap[level] || 'default'
}

/**
 * 生成缓存测试数据
 * @param {number} count - 数据项数量
 * @param {number} sizeKB - 每项数据大小(KB)
 */
export function generateCacheTestData(count = 100, sizeKB = 10) {
  const items = []

  for (let i = 0; i < count; i++) {
    // 生成指定大小的测试数据
    const baseData = {
      id: i + 1,
      name: `测试数据项_${i + 1}`,
      timestamp: Date.now(),
      index: i
    }

    // 填充数据到指定大小
    const targetSize = sizeKB * 1024 // 转换为字节
    const baseSize = JSON.stringify(baseData).length
    const paddingSize = Math.max(0, targetSize - baseSize)

    if (paddingSize > 0) {
      baseData.padding = 'x'.repeat(paddingSize)
    }

    items.push(baseData)
  }

  return items


/**
 * 生成网络请求测试数据
 */
function generateNetworkTestData() {
  return {
    // 快速响应的请求
    fast: {
      delay: 100,
      data: generateMaterialData(10)
    },
    
    // 中等响应的请求
    medium: {
      delay: 500,
      data: generateMaterialData(50)
    },
    
    // 慢响应的请求
    slow: {
      delay: 2000,
      data: generateMaterialData(100)
    },
    
    // 超时请求
    timeout: {
      delay: 10000,
      data: generateMaterialData(200)
    }
  }
}

module.exports = {
  generateMaterialData,
  generateOrderData,
  generateInventoryData,
  generateCustomerData,
  generatePerformanceTestData,
  generateCacheTestData,
  generateNetworkTestData
}
