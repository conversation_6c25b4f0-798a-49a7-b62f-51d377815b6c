/**
 * 移动端适配器
 * 用于检测移动设备并提供移动端优化配置
 */

class MobileAdapter {
  constructor() {
    this.isMobile = this.detectMobile();
    this.deviceInfo = this.getDeviceInfo();
    this.viewportConfig = this.getViewportConfig();
  }

  /**
   * 检测是否为移动设备
   */
  detectMobile() {
    if (typeof window === 'undefined') return false;
    
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
    
    // 检查用户代理
    const isMobileUA = mobileRegex.test(userAgent);
    
    // 检查屏幕尺寸
    const isMobileScreen = window.innerWidth <= 768;
    
    // 检查触摸支持
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    return isMobileUA || (isMobileScreen && isTouchDevice);
  }

  /**
   * 获取设备信息
   */
  getDeviceInfo() {
    if (typeof window === 'undefined') {
      return {
        type: 'unknown',
        os: 'unknown',
        browser: 'unknown'
      };
    }

    const userAgent = navigator.userAgent;
    
    // 设备类型
    let deviceType = 'desktop';
    if (/iPad/i.test(userAgent)) {
      deviceType = 'tablet';
    } else if (/iPhone|iPod|Android.*Mobile/i.test(userAgent)) {
      deviceType = 'mobile';
    } else if (/Android/i.test(userAgent)) {
      deviceType = 'tablet';
    }

    // 操作系统
    let os = 'unknown';
    if (/iPhone|iPad|iPod/i.test(userAgent)) {
      os = 'ios';
    } else if (/Android/i.test(userAgent)) {
      os = 'android';
    } else if (/Windows/i.test(userAgent)) {
      os = 'windows';
    } else if (/Mac/i.test(userAgent)) {
      os = 'macos';
    }

    // 浏览器
    let browser = 'unknown';
    if (/Chrome/i.test(userAgent)) {
      browser = 'chrome';
    } else if (/Safari/i.test(userAgent)) {
      browser = 'safari';
    } else if (/Firefox/i.test(userAgent)) {
      browser = 'firefox';
    } else if (/Edge/i.test(userAgent)) {
      browser = 'edge';
    }

    return {
      type: deviceType,
      os,
      browser,
      userAgent
    };
  }

  /**
   * 获取视口配置
   */
  getViewportConfig() {
    if (typeof window === 'undefined') {
      return {
        width: 375,
        height: 667,
        ratio: 1
      };
    }

    return {
      width: window.innerWidth,
      height: window.innerHeight,
      ratio: window.devicePixelRatio || 1,
      orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
    };
  }

  /**
   * 获取组件配置
   */
  getComponentConfig(componentType) {
    const baseConfig = {
      // 表格配置
      table: {
        size: this.isMobile ? 'small' : 'default',
        scroll: this.isMobile ? { x: true } : undefined,
        pagination: {
          size: this.isMobile ? 'small' : 'default',
          showSizeChanger: !this.isMobile,
          showQuickJumper: !this.isMobile,
          showTotal: !this.isMobile,
          pageSize: this.isMobile ? 10 : 20
        }
      },

      // 表单配置
      form: {
        layout: this.isMobile ? 'vertical' : 'horizontal',
        labelCol: this.isMobile ? undefined : { span: 6 },
        wrapperCol: this.isMobile ? undefined : { span: 18 },
        size: this.isMobile ? 'large' : 'default'
      },

      // 按钮配置
      button: {
        size: this.isMobile ? 'large' : 'default',
        block: this.isMobile
      },

      // 输入框配置
      input: {
        size: this.isMobile ? 'large' : 'default'
      },

      // 选择器配置
      select: {
        size: this.isMobile ? 'large' : 'default',
        showSearch: true,
        optionFilterProp: 'children'
      },

      // 日期选择器配置
      datePicker: {
        size: this.isMobile ? 'large' : 'default',
        format: 'YYYY-MM-DD'
      },

      // 模态框配置
      modal: {
        width: this.isMobile ? '95%' : 800,
        centered: this.isMobile,
        maskClosable: !this.isMobile
      },

      // 抽屉配置
      drawer: {
        width: this.isMobile ? '100%' : 600,
        placement: this.isMobile ? 'bottom' : 'right'
      }
    };

    return componentType ? baseConfig[componentType] : baseConfig;
  }

  /**
   * 获取样式配置
   */
  getStyleConfig() {
    return {
      // 间距配置
      spacing: {
        xs: this.isMobile ? 8 : 4,
        sm: this.isMobile ? 12 : 8,
        md: this.isMobile ? 16 : 12,
        lg: this.isMobile ? 24 : 16,
        xl: this.isMobile ? 32 : 24
      },

      // 字体配置
      fontSize: {
        xs: this.isMobile ? 12 : 10,
        sm: this.isMobile ? 14 : 12,
        md: this.isMobile ? 16 : 14,
        lg: this.isMobile ? 18 : 16,
        xl: this.isMobile ? 20 : 18
      },

      // 圆角配置
      borderRadius: {
        sm: this.isMobile ? 4 : 2,
        md: this.isMobile ? 6 : 4,
        lg: this.isMobile ? 8 : 6
      }
    };
  }

  /**
   * 设置视口元标签
   */
  setViewportMeta() {
    if (typeof document === 'undefined') return;

    let viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport) {
      viewport = document.createElement('meta');
      viewport.name = 'viewport';
      document.head.appendChild(viewport);
    }

    if (this.isMobile) {
      viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
    } else {
      viewport.content = 'width=device-width, initial-scale=1.0';
    }
  }

  /**
   * 添加移动端样式类
   */
  addMobileClass() {
    if (typeof document === 'undefined') return;

    const body = document.body;
    if (this.isMobile) {
      body.classList.add('mobile-device');
      body.classList.add(`mobile-${this.deviceInfo.type}`);
      body.classList.add(`mobile-${this.deviceInfo.os}`);
    } else {
      body.classList.add('desktop-device');
    }
  }

  /**
   * 初始化移动端适配
   */
  init() {
    this.setViewportMeta();
    this.addMobileClass();
    
    // 监听屏幕方向变化
    if (typeof window !== 'undefined') {
      window.addEventListener('orientationchange', () => {
        setTimeout(() => {
          this.viewportConfig = this.getViewportConfig();
        }, 100);
      });

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        this.viewportConfig = this.getViewportConfig();
      });
    }
  }
}

// 创建全局实例
const mobileAdapter = new MobileAdapter();

// 自动初始化
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    mobileAdapter.init();
  });
}

export default mobileAdapter;
