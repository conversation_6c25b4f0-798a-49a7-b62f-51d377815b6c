#!/bin/bash

# jshERP Development Environment Setup Script
set -e

echo "🚀 Setting up jshERP development environment..."

# Update system packages
sudo apt-get update -y

# Install Java 8 (required for jshERP)
echo "📦 Installing Java 8..."
sudo apt-get install -y openjdk-8-jdk
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
echo 'export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64' >> $HOME/.profile
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> $HOME/.profile

# Install Maven
echo "📦 Installing Maven..."
sudo apt-get install -y maven

# Install Node.js and npm (for frontend)
echo "📦 Installing Node.js..."
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install MySQL client (for database connectivity tests)
echo "📦 Installing MySQL client..."
sudo apt-get install -y mysql-client

# Verify installations
echo "✅ Verifying installations..."
java -version
mvn -version
node --version
npm --version

# Navigate to backend directory and install dependencies
echo "🔧 Setting up backend dependencies..."
cd /mnt/persist/workspace/jshERP-boot

# Create test directory structure if it doesn't exist
mkdir -p src/test/java/com/jsh/erp/service
mkdir -p src/test/resources

# Create a simple test class to verify the setup
cat > src/test/java/com/jsh/erp/service/BasicSetupTest.java << 'EOF'
package com.jsh.erp.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import static org.junit.Assert.*;

@RunWith(SpringRunner.class)
@SpringBootTest
public class BasicSetupTest {

    @Test
    public void testApplicationContextLoads() {
        // This test will pass if the Spring context loads successfully
        assertTrue("Application context should load", true);
    }

    @Test
    public void testBasicJavaFunctionality() {
        String expected = "jshERP";
        String actual = "jshERP";
        assertEquals("Basic string comparison should work", expected, actual);
    }

    @Test
    public void testMathOperations() {
        int result = 2 + 2;
        assertEquals("Basic math should work", 4, result);
    }
}
EOF

# Create test configuration if it doesn't exist
if [ ! -f src/test/resources/application-test.properties ]; then
    cat > src/test/resources/application-test.properties << 'EOF'
# Test environment configuration
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# H2 console
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false

# Logging
logging.level.com.jsh.erp=WARN
logging.level.org.springframework=WARN
logging.level.org.mybatis=WARN

# Test profile
spring.profiles.active=test
EOF
fi

# Compile the project to download dependencies
echo "📥 Downloading Maven dependencies..."
mvn clean compile test-compile -DskipTests=true

# Setup frontend dependencies
echo "🔧 Setting up frontend dependencies..."
cd /mnt/persist/workspace/jshERP-web

# Install npm dependencies
npm install

echo "✅ Setup completed successfully!"
echo "📋 Environment Summary:"
echo "  - Java: $(java -version 2>&1 | head -1)"
echo "  - Maven: $(mvn -version | head -1)"
echo "  - Node.js: $(node --version)"
echo "  - npm: $(npm --version)"
echo ""
echo "🧪 Ready to run tests!"