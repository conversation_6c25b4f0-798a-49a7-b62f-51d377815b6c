#!/bin/bash

# jshERP Test Environment Setup
set -e

echo "Setting up jshERP test environment..."

# Install Java 8
sudo apt-get update -q
sudo apt-get install -y openjdk-8-jdk maven

# Set Java environment
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
echo 'export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64' >> $HOME/.profile
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> $HOME/.profile

# Navigate to backend directory
cd /mnt/persist/workspace/jshERP-boot

# Create test directory structure
mkdir -p src/test/java/com/jsh/erp/service
mkdir -p src/test/resources

# Create basic test class
cat > src/test/java/com/jsh/erp/service/BasicSetupTest.java << 'EOF'
package com.jsh.erp.service;

import org.junit.Test;
import static org.junit.Assert.*;

public class BasicSetupTest {

    @Test
    public void testBasicFunctionality() {
        assertEquals("Basic test should pass", "test", "test");
    }

    @Test
    public void testMathOperations() {
        assertEquals("Math should work", 4, 2 + 2);
    }
}
EOF

# Create minimal test properties
cat > src/test/resources/application-test.properties << 'EOF'
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.profiles.active=test
logging.level.root=WARN
EOF

echo "Setup completed!"