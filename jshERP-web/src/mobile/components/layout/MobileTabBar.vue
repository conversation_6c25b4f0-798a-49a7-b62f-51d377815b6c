<template>
  <div class="mobile-tab-bar" v-if="shouldShowTabBar">
    <van-tabbar 
      v-model="activeTab" 
      :fixed="fixed"
      :placeholder="placeholder"
      :z-index="zIndex"
      :safe-area-inset-bottom="safeAreaInsetBottom"
      @change="handleTabChange"
    >
      <!-- 首页 -->
      <van-tabbar-item 
        v-if="hasPermission('dashboard')"
        name="dashboard" 
        icon="home-o"
        :badge="dashboardBadge"
      >
        首页
      </van-tabbar-item>
      
      <!-- 商品管理 -->
      <van-tabbar-item 
        v-if="hasPermission('product')"
        name="products" 
        icon="goods-collect-o"
        :badge="productBadge"
      >
        商品
      </van-tabbar-item>
      
      <!-- 销售管理 -->
      <van-tabbar-item 
        v-if="hasPermission('sales')"
        name="sales" 
        icon="shopping-cart-o"
        :badge="salesBadge"
      >
        销售
      </van-tabbar-item>
      
      <!-- 库存管理 -->
      <van-tabbar-item 
        v-if="hasPermission('inventory')"
        name="inventory" 
        icon="records"
        :badge="inventoryBadge"
      >
        库存
      </van-tabbar-item>
      
      <!-- 个人中心 -->
      <van-tabbar-item 
        name="profile" 
        icon="user-o"
        :badge="profileBadge"
      >
        我的
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

/**
 * 移动端底部导航标签栏组件
 * 提供主要业务模块的快速导航
 */
export default {
  name: 'MobileTabBar',
  
  props: {
    // 是否固定定位
    fixed: {
      type: Boolean,
      default: true
    },
    
    // 是否显示占位元素
    placeholder: {
      type: Boolean,
      default: true
    },
    
    // 层级
    zIndex: {
      type: Number,
      default: 1000
    },
    
    // 是否适配安全区域
    safeAreaInsetBottom: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      activeTab: 'dashboard'
    }
  },
  
  computed: {
    ...mapGetters(['userInfo', 'permissions']),
    
    /**
     * 是否应该显示标签栏
     */
    shouldShowTabBar() {
      return this.$route.meta.showTabBar !== false
    },
    
    /**
     * 首页徽章
     */
    dashboardBadge() {
      // 可以显示待处理任务数量等
      const pendingTasks = this.$store.getters.pendingTasksCount || 0
      return pendingTasks > 0 ? pendingTasks : null
    },
    
    /**
     * 商品徽章
     */
    productBadge() {
      // 可以显示库存预警商品数量
      const lowStockCount = this.$store.getters.lowStockProductsCount || 0
      return lowStockCount > 0 ? lowStockCount : null
    },
    
    /**
     * 销售徽章
     */
    salesBadge() {
      // 可以显示待处理订单数量
      const pendingOrders = this.$store.getters.pendingOrdersCount || 0
      return pendingOrders > 0 ? pendingOrders : null
    },
    
    /**
     * 库存徽章
     */
    inventoryBadge() {
      // 可以显示待盘点数量
      const pendingChecks = this.$store.getters.pendingStockChecksCount || 0
      return pendingChecks > 0 ? pendingChecks : null
    },
    
    /**
     * 个人中心徽章
     */
    profileBadge() {
      // 可以显示未读消息数量
      const unreadMessages = this.$store.getters.unreadMessagesCount || 0
      return unreadMessages > 0 ? unreadMessages : null
    }
  },
  
  watch: {
    /**
     * 监听路由变化，更新活跃标签
     */
    '$route'(to) {
      this.updateActiveTab(to)
    }
  },
  
  created() {
    // 初始化活跃标签
    this.updateActiveTab(this.$route)
  },
  
  methods: {
    /**
     * 检查用户权限
     */
    hasPermission(module) {
      // 个人中心所有用户都可以访问
      if (module === 'profile') {
        return true
      }
      
      // 检查具体模块权限
      const permissionMap = {
        dashboard: 'dashboard:view',
        product: 'product:view',
        sales: 'sales:view',
        inventory: 'inventory:view'
      }
      
      const permission = permissionMap[module]
      if (!permission) return true
      
      // 这里应该调用实际的权限检查逻辑
      return this.$store.getters.hasPermission && this.$store.getters.hasPermission(permission)
    },
    
    /**
     * 处理标签切换
     */
    handleTabChange(name) {
      const routeMap = {
        dashboard: '/m/dashboard',
        products: '/m/products',
        sales: '/m/sales',
        inventory: '/m/inventory',
        profile: '/m/profile'
      }
      
      const targetRoute = routeMap[name]
      if (targetRoute && this.$route.path !== targetRoute) {
        this.$router.push(targetRoute)
      }
      
      this.$emit('tab-change', name)
    },
    
    /**
     * 更新活跃标签
     */
    updateActiveTab(route) {
      const path = route.path
      
      if (path.startsWith('/m/dashboard')) {
        this.activeTab = 'dashboard'
      } else if (path.startsWith('/m/products') || path.startsWith('/m/categories')) {
        this.activeTab = 'products'
      } else if (path.startsWith('/m/sales') || path.startsWith('/m/orders') || path.startsWith('/m/customers')) {
        this.activeTab = 'sales'
      } else if (path.startsWith('/m/inventory') || path.startsWith('/m/stock')) {
        this.activeTab = 'inventory'
      } else if (path.startsWith('/m/profile') || path.startsWith('/m/settings')) {
        this.activeTab = 'profile'
      } else {
        // 对于其他页面，根据meta.tab设置
        this.activeTab = route.meta.tab || 'dashboard'
      }
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-tab-bar {
  // Vant Tabbar 样式定制
  :deep(.van-tabbar) {
    background: #ffffff;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    border-top: 1px solid #f0f0f0;
    
    .van-tabbar-item {
      &__text {
        font-size: 12px;
        margin-top: 4px;
      }
      
      &__icon {
        font-size: 20px;
        margin-bottom: 2px;
      }
      
      // 默认状态
      &:not(.van-tabbar-item--active) {
        .van-tabbar-item__text {
          color: #969799;
        }
        
        .van-tabbar-item__icon {
          color: #969799;
        }
      }
      
      // 激活状态
      &--active {
        .van-tabbar-item__text {
          color: #3B82F6;
          font-weight: 500;
        }
        
        .van-tabbar-item__icon {
          color: #3B82F6;
        }
      }
      
      // 徽章样式
      .van-badge {
        .van-badge__wrapper {
          .van-badge--dot {
            background: #ff4757;
          }
          
          .van-badge__content {
            background: #ff4757;
            color: #ffffff;
            font-size: 10px;
            min-width: 16px;
            height: 16px;
            line-height: 16px;
            border-radius: 8px;
          }
        }
      }
    }
  }
}

// 安全区域适配
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .mobile-tab-bar {
    :deep(.van-tabbar) {
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .mobile-tab-bar {
    :deep(.van-tabbar) {
      .van-tabbar-item {
        &__text {
          font-size: 11px;
        }
        
        &__icon {
          font-size: 18px;
        }
      }
    }
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-tab-bar {
    :deep(.van-tabbar) {
      height: 45px;
      
      .van-tabbar-item {
        &__text {
          font-size: 10px;
          margin-top: 2px;
        }
        
        &__icon {
          font-size: 16px;
          margin-bottom: 1px;
        }
      }
    }
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .mobile-tab-bar {
    :deep(.van-tabbar) {
      background: #1f2937;
      border-top-color: #374151;
      
      .van-tabbar-item {
        &:not(.van-tabbar-item--active) {
          .van-tabbar-item__text,
          .van-tabbar-item__icon {
            color: #9ca3af;
          }
        }
      }
    }
  }
}
</style>
