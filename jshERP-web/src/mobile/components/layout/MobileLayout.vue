<template>
  <div class="mobile-layout" :class="layoutClass">
    <!-- 头部导航 -->
    <mobile-header
      v-if="showHeader"
      :title="currentTitle"
      :show-back="showBack"
      :show-notification="showNotification"
      :custom-class="headerClass"
      @left-click="handleHeaderLeftClick"
      @right-click="handleHeaderRightClick"
      @notification-click="handleNotificationClick"
    >
      <!-- 头部插槽 -->
      <template #left v-if="$slots.headerLeft">
        <slot name="headerLeft"></slot>
      </template>
      
      <template #title v-if="$slots.headerTitle">
        <slot name="headerTitle"></slot>
      </template>
      
      <template #right v-if="$slots.headerRight">
        <slot name="headerRight"></slot>
      </template>
    </mobile-header>
    
    <!-- 主内容区域 -->
    <div class="mobile-content" :class="contentClass">
      <!-- 下拉刷新 -->
      <van-pull-refresh
        v-if="enablePullRefresh"
        v-model="refreshing"
        @refresh="handleRefresh"
        :success-text="refreshSuccessText"
        :success-duration="1500"
      >
        <router-view />
      </van-pull-refresh>
      
      <!-- 普通内容 -->
      <router-view v-else />
      
      <!-- 回到顶部按钮 -->
      <van-back-top
        v-if="showBackTop"
        :right="20"
        :bottom="showTabBar ? 80 : 20"
        :z-index="999"
      />
    </div>
    
    <!-- 底部导航 -->
    <mobile-tab-bar
      v-if="showTabBar"
      @tab-change="handleTabChange"
    />
    
    <!-- 全局加载遮罩 -->
    <van-overlay 
      v-if="globalLoading" 
      :show="globalLoading"
      :z-index="9999"
    >
      <div class="global-loading">
        <van-loading type="spinner" size="24px" color="#3B82F6" />
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </van-overlay>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import MobileHeader from './MobileHeader.vue'
import MobileTabBar from './MobileTabBar.vue'

/**
 * 移动端主布局组件
 * 整合头部导航、内容区域、底部导航等核心布局元素
 */
export default {
  name: 'MobileLayout',
  
  components: {
    MobileHeader,
    MobileTabBar
  },
  
  data() {
    return {
      refreshing: false,
      globalLoading: false,
      loadingText: '加载中...'
    }
  },
  
  computed: {
    ...mapGetters(['userInfo']),
    
    /**
     * 当前页面标题
     */
    currentTitle() {
      return this.$route.meta.title || 'jshERP'
    },
    
    /**
     * 是否显示头部
     */
    showHeader() {
      return this.$route.meta.showHeader !== false
    },
    
    /**
     * 是否显示返回按钮
     */
    showBack() {
      return this.$route.meta.showBack !== false && this.$route.path !== '/m/dashboard'
    },
    
    /**
     * 是否显示通知图标
     */
    showNotification() {
      return this.$route.meta.showNotification === true
    },
    
    /**
     * 是否显示底部导航
     */
    showTabBar() {
      return this.$route.meta.showTabBar !== false
    },
    
    /**
     * 是否启用下拉刷新
     */
    enablePullRefresh() {
      return this.$route.meta.enablePullRefresh === true
    },
    
    /**
     * 是否显示回到顶部按钮
     */
    showBackTop() {
      return this.$route.meta.showBackTop !== false
    },
    
    /**
     * 刷新成功文本
     */
    refreshSuccessText() {
      return this.$route.meta.refreshSuccessText || '刷新成功'
    },
    
    /**
     * 布局样式类
     */
    layoutClass() {
      return {
        'mobile-layout--no-header': !this.showHeader,
        'mobile-layout--no-tabbar': !this.showTabBar,
        'mobile-layout--full-screen': this.$route.meta.fullScreen,
        'mobile-layout--transparent': this.$route.meta.transparentLayout
      }
    },
    
    /**
     * 头部样式类
     */
    headerClass() {
      return this.$route.meta.headerClass || ''
    },
    
    /**
     * 内容区域样式类
     */
    contentClass() {
      return {
        'mobile-content--no-padding': this.$route.meta.noPadding,
        'mobile-content--gray-bg': this.$route.meta.grayBackground
      }
    }
  },
  
  created() {
    // 监听全局加载状态
    this.$store.subscribe((mutation) => {
      if (mutation.type === 'SET_GLOBAL_LOADING') {
        this.globalLoading = mutation.payload.loading
        this.loadingText = mutation.payload.text || '加载中...'
      }
    })
  },
  
  methods: {
    /**
     * 处理头部左侧点击
     */
    handleHeaderLeftClick() {
      this.$emit('header-left-click')
    },
    
    /**
     * 处理头部右侧点击
     */
    handleHeaderRightClick() {
      this.$emit('header-right-click')
    },
    
    /**
     * 处理通知点击
     */
    handleNotificationClick() {
      this.$emit('notification-click')
    },
    
    /**
     * 处理标签栏切换
     */
    handleTabChange(tab) {
      this.$emit('tab-change', tab)
    },
    
    /**
     * 处理下拉刷新
     */
    async handleRefresh() {
      try {
        // 触发页面刷新事件
        this.$emit('refresh')
        
        // 如果页面组件有refresh方法，调用它
        if (this.$refs.pageComponent && this.$refs.pageComponent.refresh) {
          await this.$refs.pageComponent.refresh()
        }
        
        // 延迟一下，让用户看到刷新效果
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.error('刷新失败:', error)
        this.$toast('刷新失败，请重试')
      } finally {
        this.refreshing = false
      }
    },
    
    /**
     * 显示全局加载
     */
    showGlobalLoading(text = '加载中...') {
      this.$store.commit('SET_GLOBAL_LOADING', { loading: true, text })
    },
    
    /**
     * 隐藏全局加载
     */
    hideGlobalLoading() {
      this.$store.commit('SET_GLOBAL_LOADING', { loading: false })
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f7f8fa;
  
  .mobile-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    position: relative;
    
    // 默认内边距
    &:not(.mobile-content--no-padding) {
      padding: 16px;
    }
    
    // 灰色背景
    &.mobile-content--gray-bg {
      background: #f7f8fa;
    }
  }
  
  // 无头部布局
  &.mobile-layout--no-header {
    .mobile-content {
      padding-top: env(safe-area-inset-top);
    }
  }
  
  // 无底部导航布局
  &.mobile-layout--no-tabbar {
    .mobile-content {
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
  
  // 全屏布局
  &.mobile-layout--full-screen {
    .mobile-content {
      padding: 0;
    }
  }
  
  // 透明布局
  &.mobile-layout--transparent {
    background: transparent;
    
    .mobile-content {
      background: transparent;
    }
  }
}

// 全局加载遮罩
.global-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  
  .loading-text {
    margin-top: 12px;
    font-size: 14px;
    color: #3B82F6;
  }
}

// 安全区域适配
@supports (padding-top: env(safe-area-inset-top)) {
  .mobile-layout {
    &.mobile-layout--no-header {
      .mobile-content {
        padding-top: calc(16px + env(safe-area-inset-top));
      }
    }
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .mobile-layout {
    &.mobile-layout--no-tabbar {
      .mobile-content {
        padding-bottom: calc(16px + env(safe-area-inset-bottom));
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .mobile-layout {
    .mobile-content {
      &:not(.mobile-content--no-padding) {
        padding: 12px;
      }
    }
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-layout {
    .mobile-content {
      &:not(.mobile-content--no-padding) {
        padding: 8px 16px;
      }
    }
  }
}
</style>
