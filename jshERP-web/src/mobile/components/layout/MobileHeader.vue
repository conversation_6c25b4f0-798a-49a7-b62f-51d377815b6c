<template>
  <div class="mobile-header" :class="headerClass">
    <van-nav-bar
      :title="currentTitle"
      :left-text="leftText"
      :left-arrow="showBack"
      :right-text="rightText"
      :fixed="fixed"
      :placeholder="placeholder"
      :z-index="zIndex"
      @click-left="handleLeftClick"
      @click-right="handleRightClick"
    >
      <!-- 左侧插槽 -->
      <template #left v-if="$slots.left">
        <slot name="left"></slot>
      </template>
      
      <!-- 标题插槽 -->
      <template #title v-if="$slots.title">
        <slot name="title"></slot>
      </template>
      
      <!-- 右侧插槽 -->
      <template #right v-if="$slots.right">
        <slot name="right"></slot>
      </template>
    </van-nav-bar>
    
    <!-- 消息通知图标 -->
    <div 
      v-if="showNotification" 
      class="mobile-header-notification"
      @click="handleNotificationClick"
    >
      <van-icon name="bell" size="20" />
      <van-badge 
        v-if="notificationCount > 0" 
        :content="notificationCount > 99 ? '99+' : notificationCount"
        class="notification-badge"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

/**
 * 移动端头部导航组件
 * 提供统一的页面头部导航功能
 */
export default {
  name: 'MobileHeader',
  
  props: {
    // 页面标题
    title: {
      type: String,
      default: ''
    },
    
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      default: true
    },
    
    // 左侧文本
    leftText: {
      type: String,
      default: ''
    },
    
    // 右侧文本
    rightText: {
      type: String,
      default: ''
    },
    
    // 是否固定定位
    fixed: {
      type: Boolean,
      default: true
    },
    
    // 是否显示占位元素
    placeholder: {
      type: Boolean,
      default: true
    },
    
    // 层级
    zIndex: {
      type: Number,
      default: 1000
    },
    
    // 是否显示通知图标
    showNotification: {
      type: Boolean,
      default: false
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    }
  },
  
  computed: {
    ...mapGetters(['userInfo']),
    
    /**
     * 当前页面标题
     */
    currentTitle() {
      return this.title || this.$route.meta.title || 'jshERP'
    },
    
    /**
     * 头部样式类
     */
    headerClass() {
      return {
        'mobile-header--transparent': this.$route.meta.transparentHeader,
        'mobile-header--dark': this.$route.meta.darkHeader,
        [this.customClass]: this.customClass
      }
    },
    
    /**
     * 通知数量
     */
    notificationCount() {
      // 这里可以从store或API获取通知数量
      return this.$store.getters.notificationCount || 0
    }
  },
  
  methods: {
    /**
     * 处理左侧点击事件
     */
    handleLeftClick() {
      if (this.$route.meta.customBack) {
        // 自定义返回逻辑
        this.$route.meta.customBack()
      } else if (this.showBack) {
        // 默认返回逻辑
        this.goBack()
      }
      
      this.$emit('left-click')
    },
    
    /**
     * 处理右侧点击事件
     */
    handleRightClick() {
      this.$emit('right-click')
    },
    
    /**
     * 处理通知点击事件
     */
    handleNotificationClick() {
      this.$router.push('/m/notifications')
      this.$emit('notification-click')
    },
    
    /**
     * 返回上一页
     */
    goBack() {
      if (window.history.length > 1) {
        this.$router.go(-1)
      } else {
        // 如果没有历史记录，跳转到首页
        this.$router.push('/m/dashboard')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-header {
  position: relative;
  
  // Vant NavBar 样式定制
  :deep(.van-nav-bar) {
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .van-nav-bar__title {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
    }
    
    .van-nav-bar__left,
    .van-nav-bar__right {
      .van-icon {
        color: #333333;
        font-size: 18px;
      }
      
      .van-nav-bar__text {
        color: #3B82F6;
        font-size: 14px;
      }
    }
  }
  
  // 通知图标
  .mobile-header-notification {
    position: absolute;
    top: 50%;
    right: 16px;
    transform: translateY(-50%);
    z-index: 1001;
    cursor: pointer;
    padding: 8px;
    
    .van-icon {
      color: #333333;
    }
    
    .notification-badge {
      position: absolute;
      top: 2px;
      right: 2px;
      transform: translate(50%, -50%);
    }
    
    &:active {
      opacity: 0.7;
    }
  }
  
  // 透明头部样式
  &.mobile-header--transparent {
    :deep(.van-nav-bar) {
      background: transparent;
      box-shadow: none;
      
      .van-nav-bar__title,
      .van-nav-bar__left .van-icon,
      .van-nav-bar__right .van-icon {
        color: #ffffff;
      }
    }
    
    .mobile-header-notification {
      .van-icon {
        color: #ffffff;
      }
    }
  }
  
  // 深色头部样式
  &.mobile-header--dark {
    :deep(.van-nav-bar) {
      background: #1f2937;
      
      .van-nav-bar__title,
      .van-nav-bar__left .van-icon,
      .van-nav-bar__right .van-icon {
        color: #ffffff;
      }
    }
    
    .mobile-header-notification {
      .van-icon {
        color: #ffffff;
      }
    }
  }
}

// 安全区域适配
@supports (padding-top: env(safe-area-inset-top)) {
  .mobile-header {
    :deep(.van-nav-bar) {
      padding-top: env(safe-area-inset-top);
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .mobile-header {
    :deep(.van-nav-bar) {
      .van-nav-bar__title {
        font-size: 15px;
      }
    }
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-header {
    :deep(.van-nav-bar) {
      height: 40px;
      
      .van-nav-bar__title {
        font-size: 14px;
      }
    }
    
    .mobile-header-notification {
      right: 12px;
      padding: 6px;
    }
  }
}
</style>
