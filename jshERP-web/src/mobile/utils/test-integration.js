/**
 * 移动端集成测试工具
 * 用于验证移动端各个模块的集成情况
 */

import { detectDevice } from '@/shared/utils/device'
import store from '@/store'

/**
 * 测试设备检测功能
 */
export function testDeviceDetection() {
  console.group('🔍 设备检测测试')
  
  const device = detectDevice()
  console.log('设备信息:', device)
  
  const tests = [
    {
      name: '设备类型检测',
      test: () => typeof device.isMobile === 'boolean',
      expected: true
    },
    {
      name: '用户代理检测',
      test: () => typeof device.userAgent === 'string',
      expected: true
    },
    {
      name: '屏幕尺寸检测',
      test: () => typeof device.screenWidth === 'number',
      expected: true
    }
  ]
  
  tests.forEach(({ name, test, expected }) => {
    const result = test()
    const passed = result === expected
    console.log(`${passed ? '✅' : '❌'} ${name}: ${result}`)
  })
  
  console.groupEnd()
  return tests.every(({ test, expected }) => test() === expected)
}

/**
 * 测试Store集成
 */
export function testStoreIntegration() {
  console.group('🗄️ Store集成测试')
  
  const tests = [
    {
      name: 'Dashboard模块注册',
      test: () => !!store._modules.root._children.dashboard,
      expected: true
    },
    {
      name: '全局加载状态',
      test: () => typeof store.state.globalLoading === 'boolean',
      expected: true
    },
    {
      name: 'Dashboard getters',
      test: () => typeof store.getters.dashboardStatistics === 'object',
      expected: true
    },
    {
      name: 'User模块存在',
      test: () => !!store._modules.root._children.user,
      expected: true
    }
  ]
  
  tests.forEach(({ name, test, expected }) => {
    try {
      const result = test()
      const passed = result === expected
      console.log(`${passed ? '✅' : '❌'} ${name}: ${result}`)
    } catch (error) {
      console.log(`❌ ${name}: Error - ${error.message}`)
    }
  })
  
  console.groupEnd()
  return tests.every(({ test, expected }) => {
    try {
      return test() === expected
    } catch {
      return false
    }
  })
}

/**
 * 测试路由配置
 */
export function testRouterConfiguration() {
  console.group('🛣️ 路由配置测试')
  
  const router = window.app?.$router
  if (!router) {
    console.log('❌ 路由实例未找到')
    console.groupEnd()
    return false
  }
  
  const routes = router.options.routes
  const mobileRoutes = routes.filter(route => route.path?.startsWith('/m'))
  
  const tests = [
    {
      name: '移动端路由存在',
      test: () => mobileRoutes.length > 0,
      expected: true
    },
    {
      name: 'Dashboard路由存在',
      test: () => routes.some(route => 
        route.path === '/m' || 
        (route.children && route.children.some(child => child.path === 'dashboard'))
      ),
      expected: true
    },
    {
      name: '认证路由存在',
      test: () => routes.some(route => 
        route.path === '/m/auth' ||
        (route.children && route.children.some(child => child.path?.includes('auth')))
      ),
      expected: true
    }
  ]
  
  tests.forEach(({ name, test, expected }) => {
    try {
      const result = test()
      const passed = result === expected
      console.log(`${passed ? '✅' : '❌'} ${name}: ${result}`)
    } catch (error) {
      console.log(`❌ ${name}: Error - ${error.message}`)
    }
  })
  
  console.log('📋 移动端路由列表:')
  mobileRoutes.forEach(route => {
    console.log(`  - ${route.path}`)
  })
  
  console.groupEnd()
  return tests.every(({ test, expected }) => {
    try {
      return test() === expected
    } catch {
      return false
    }
  })
}

/**
 * 测试组件库集成
 */
export function testComponentLibraries() {
  console.group('🧩 组件库集成测试')
  
  const tests = [
    {
      name: 'Vue实例存在',
      test: () => !!window.Vue,
      expected: true
    },
    {
      name: 'Vant组件注册',
      test: () => {
        const vueInstance = window.app
        return vueInstance && vueInstance.$options.components && 
               Object.keys(vueInstance.$options.components).some(name => name.startsWith('van-'))
      },
      expected: true
    },
    {
      name: 'Ant Design Vue存在',
      test: () => {
        const vueInstance = window.app
        return vueInstance && vueInstance.$options.components &&
               Object.keys(vueInstance.$options.components).some(name => name.startsWith('a-'))
      },
      expected: true
    }
  ]
  
  tests.forEach(({ name, test, expected }) => {
    try {
      const result = test()
      const passed = result === expected
      console.log(`${passed ? '✅' : '❌'} ${name}: ${result}`)
    } catch (error) {
      console.log(`❌ ${name}: Error - ${error.message}`)
    }
  })
  
  console.groupEnd()
  return tests.every(({ test, expected }) => {
    try {
      return test() === expected
    } catch {
      return false
    }
  })
}

/**
 * 测试API配置
 */
export function testAPIConfiguration() {
  console.group('🌐 API配置测试')
  
  const tests = [
    {
      name: 'Axios实例存在',
      test: () => !!window.axios || !!window.app?.$http,
      expected: true
    },
    {
      name: 'API基础路径配置',
      test: () => {
        const baseURL = window.app?.$http?.defaults?.baseURL || 
                       window.axios?.defaults?.baseURL
        return typeof baseURL === 'string' && baseURL.length > 0
      },
      expected: true
    }
  ]
  
  tests.forEach(({ name, test, expected }) => {
    try {
      const result = test()
      const passed = result === expected
      console.log(`${passed ? '✅' : '❌'} ${name}: ${result}`)
    } catch (error) {
      console.log(`❌ ${name}: Error - ${error.message}`)
    }
  })
  
  console.groupEnd()
  return tests.every(({ test, expected }) => {
    try {
      return test() === expected
    } catch {
      return false
    }
  })
}

/**
 * 运行所有集成测试
 */
export function runAllTests() {
  console.group('🚀 jshERP移动端集成测试')
  console.log('开始运行集成测试...')
  
  const testResults = [
    { name: '设备检测', result: testDeviceDetection() },
    { name: 'Store集成', result: testStoreIntegration() },
    { name: '路由配置', result: testRouterConfiguration() },
    { name: '组件库集成', result: testComponentLibraries() },
    { name: 'API配置', result: testAPIConfiguration() }
  ]
  
  console.log('\n📊 测试结果汇总:')
  testResults.forEach(({ name, result }) => {
    console.log(`${result ? '✅' : '❌'} ${name}: ${result ? '通过' : '失败'}`)
  })
  
  const allPassed = testResults.every(({ result }) => result)
  const passedCount = testResults.filter(({ result }) => result).length
  const totalCount = testResults.length
  
  console.log(`\n🎯 总体结果: ${passedCount}/${totalCount} 项测试通过`)
  
  if (allPassed) {
    console.log('🎉 所有测试通过！移动端集成成功！')
  } else {
    console.log('⚠️ 部分测试失败，请检查相关配置')
  }
  
  console.groupEnd()
  return { allPassed, passedCount, totalCount, testResults }
}

/**
 * 在浏览器控制台中运行测试
 * 使用方法: 在控制台输入 window.runMobileTests()
 */
if (typeof window !== 'undefined') {
  window.runMobileTests = runAllTests
  window.testMobileDevice = testDeviceDetection
  window.testMobileStore = testStoreIntegration
  window.testMobileRouter = testRouterConfiguration
  window.testMobileComponents = testComponentLibraries
  window.testMobileAPI = testAPIConfiguration
}
