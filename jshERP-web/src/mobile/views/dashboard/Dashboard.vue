<template>
  <div class="mobile-dashboard">
    <!-- 用户欢迎区域 -->
    <div class="dashboard-header">
      <div class="user-welcome">
        <div class="welcome-text">
          <h2>{{ welcomeMessage }}</h2>
          <p>{{ currentDate }}</p>
        </div>
        <div class="user-avatar">
          <van-image
            :src="userAvatar"
            round
            width="48"
            height="48"
            :error-icon="'user-o'"
          />
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="dashboard-stats">
      <van-row :gutter="12">
        <van-col span="12">
          <div class="stat-card stat-card--sales">
            <div class="stat-icon">
              <van-icon name="shopping-cart-o" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.todaySale }}</div>
              <div class="stat-label">今日销售</div>
            </div>
          </div>
        </van-col>
        <van-col span="12">
          <div class="stat-card stat-card--purchase">
            <div class="stat-icon">
              <van-icon name="logistics" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.todayBuy }}</div>
              <div class="stat-label">今日采购</div>
            </div>
          </div>
        </van-col>
      </van-row>
      
      <van-row :gutter="12" style="margin-top: 12px;">
        <van-col span="12">
          <div class="stat-card stat-card--retail">
            <div class="stat-icon">
              <van-icon name="shop-o" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.todayRetail }}</div>
              <div class="stat-label">今日零售</div>
            </div>
          </div>
        </van-col>
        <van-col span="12">
          <div class="stat-card stat-card--inventory">
            <div class="stat-icon">
              <van-icon name="records" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ lowStockCount }}</div>
              <div class="stat-label">库存预警</div>
            </div>
          </div>
        </van-col>
      </van-row>
    </div>

    <!-- 快捷操作区域 -->
    <div class="dashboard-quick-actions">
      <div class="section-title">
        <h3>快捷操作</h3>
      </div>
      <van-grid :column-num="4" :gutter="12">
        <van-grid-item
          v-for="action in quickActions"
          :key="action.name"
          :icon="action.icon"
          :text="action.text"
          :badge="action.badge"
          @click="handleQuickAction(action)"
        />
      </van-grid>
    </div>

    <!-- 最近通知 -->
    <div class="dashboard-notifications" v-if="recentNotifications.length > 0">
      <div class="section-title">
        <h3>最近通知</h3>
        <van-button 
          type="primary" 
          size="mini" 
          @click="$router.push('/m/notifications')"
        >
          查看全部
        </van-button>
      </div>
      <div class="notification-list">
        <div 
          v-for="notification in recentNotifications.slice(0, 3)"
          :key="notification.id"
          class="notification-item"
          @click="handleNotificationClick(notification)"
        >
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-desc">{{ notification.content }}</div>
            <div class="notification-time">{{ formatTime(notification.createTime) }}</div>
          </div>
          <div class="notification-status" v-if="!notification.isRead">
            <van-badge dot />
          </div>
        </div>
      </div>
    </div>

    <!-- 租户信息 -->
    <div class="dashboard-tenant-info" v-if="tenantInfo.tenantId">
      <van-notice-bar
        :text="tenantNoticeText"
        :color="tenantNoticeColor"
        background="#f7f8fa"
        left-icon="info-o"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

export default {
  name: 'MobileDashboard',
  
  data() {
    return {
      refreshing: false
    }
  },
  
  computed: {
    ...mapGetters([
      'userInfo',
      'userName',
      'userRealName', 
      'userAvatar',
      'dashboardStatistics',
      'dashboardTenantInfo',
      'dashboardBadges',
      'dashboardNotifications',
      'dashboardLoading',
      'hasPermission'
    ]),
    
    /**
     * 欢迎消息
     */
    welcomeMessage() {
      const hour = new Date().getHours()
      let greeting = '早上好'
      if (hour >= 12 && hour < 18) {
        greeting = '下午好'
      } else if (hour >= 18) {
        greeting = '晚上好'
      }
      return `${greeting}，${this.userRealName || this.userName || '用户'}`
    },
    
    /**
     * 当前日期
     */
    currentDate() {
      const now = new Date()
      const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
      }
      return now.toLocaleDateString('zh-CN', options)
    },
    
    /**
     * 统计数据
     */
    statistics() {
      return this.dashboardStatistics || {}
    },
    
    /**
     * 租户信息
     */
    tenantInfo() {
      return this.dashboardTenantInfo || {}
    },
    
    /**
     * 库存预警数量
     */
    lowStockCount() {
      return this.dashboardBadges.lowStockProducts || 0
    },
    
    /**
     * 最近通知
     */
    recentNotifications() {
      return this.dashboardNotifications || []
    },
    
    /**
     * 快捷操作列表
     */
    quickActions() {
      const actions = []
      
      if (this.hasPermission('product:view')) {
        actions.push({
          name: 'products',
          text: '商品管理',
          icon: 'goods-collect-o',
          route: '/m/products',
          badge: this.dashboardBadges.lowStockProducts > 0 ? this.dashboardBadges.lowStockProducts : null
        })
      }
      
      if (this.hasPermission('sales:view')) {
        actions.push({
          name: 'sales',
          text: '销售管理', 
          icon: 'shopping-cart-o',
          route: '/m/sales',
          badge: this.dashboardBadges.pendingOrders > 0 ? this.dashboardBadges.pendingOrders : null
        })
      }
      
      if (this.hasPermission('inventory:view')) {
        actions.push({
          name: 'inventory',
          text: '库存管理',
          icon: 'records',
          route: '/m/inventory',
          badge: this.dashboardBadges.pendingStockChecks > 0 ? this.dashboardBadges.pendingStockChecks : null
        })
      }
      
      if (this.hasPermission('purchase:view')) {
        actions.push({
          name: 'purchase',
          text: '采购管理',
          icon: 'logistics',
          route: '/m/purchase'
        })
      }
      
      // 通用功能
      actions.push(
        {
          name: 'scan',
          text: '扫码查询',
          icon: 'scan',
          action: 'scan'
        },
        {
          name: 'statistics',
          text: '数据统计',
          icon: 'bar-chart-o',
          route: '/m/statistics'
        },
        {
          name: 'notifications',
          text: '消息通知',
          icon: 'bell',
          route: '/m/notifications',
          badge: this.dashboardBadges.unreadMessages > 0 ? this.dashboardBadges.unreadMessages : null
        },
        {
          name: 'profile',
          text: '个人中心',
          icon: 'user-o',
          route: '/m/profile'
        }
      )
      
      return actions
    },
    
    /**
     * 租户通知文本
     */
    tenantNoticeText() {
      if (!this.tenantInfo.tenantId) return ''
      
      const { type, expireTime, userCurrentNum, userNumLimit } = this.tenantInfo
      
      if (type === '0') {
        return `试用版本 | 到期时间：${expireTime} | 用户数：${userCurrentNum}/${userNumLimit}`
      } else {
        return `正式版本 | 服务到期：${expireTime} | 授权用户：${userCurrentNum}/${userNumLimit}`
      }
    },
    
    /**
     * 租户通知颜色
     */
    tenantNoticeColor() {
      if (!this.tenantInfo.expireTime) return '#1989fa'
      
      const expireTime = new Date(this.tenantInfo.expireTime)
      const now = new Date()
      const diffDays = Math.ceil((expireTime - now) / (1000 * 60 * 60 * 24))
      
      if (diffDays <= 5) {
        return '#ee0a24' // 红色警告
      } else if (diffDays <= 15) {
        return '#ff976a' // 橙色提醒
      } else {
        return '#1989fa' // 蓝色正常
      }
    }
  },
  
  created() {
    this.initDashboard()
  },
  
  activated() {
    // 页面激活时检查是否需要刷新
    if (this.$store.getters['dashboard/needsRefresh']) {
      this.refreshData()
    }
  },
  
  methods: {
    ...mapActions('dashboard', [
      'refreshAllData',
      'fetchStatistics',
      'fetchBadges',
      'fetchRecentNotifications'
    ]),
    
    /**
     * 初始化仪表盘
     */
    async initDashboard() {
      try {
        await this.refreshAllData()
      } catch (error) {
        console.error('初始化仪表盘失败:', error)
        this.$toast('数据加载失败，请重试')
      }
    },
    
    /**
     * 刷新数据
     */
    async refreshData() {
      this.refreshing = true
      try {
        await this.refreshAllData()
        this.$toast.success('刷新成功')
      } catch (error) {
        console.error('刷新数据失败:', error)
        this.$toast('刷新失败，请重试')
      } finally {
        this.refreshing = false
      }
    },
    
    /**
     * 处理快捷操作点击
     */
    handleQuickAction(action) {
      if (action.route) {
        this.$router.push(action.route)
      } else if (action.action === 'scan') {
        // 扫码功能
        this.handleScan()
      }
    },
    
    /**
     * 处理扫码
     */
    handleScan() {
      // 这里可以集成扫码功能
      this.$toast('扫码功能开发中...')
    },
    
    /**
     * 处理通知点击
     */
    handleNotificationClick(notification) {
      // 标记为已读并跳转到详情
      this.$router.push(`/m/notifications/${notification.id}`)
    },
    
    /**
     * 格式化时间
     */
    formatTime(time) {
      if (!time) return ''
      return formatDistanceToNow(new Date(time), { 
        addSuffix: true, 
        locale: zhCN 
      })
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-dashboard {
  background: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 16px;
  
  .dashboard-header {
    background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
    padding: 24px 16px 32px;
    color: white;
    
    .user-welcome {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .welcome-text {
        h2 {
          font-size: 20px;
          font-weight: 600;
          margin: 0 0 4px 0;
        }
        
        p {
          font-size: 14px;
          opacity: 0.9;
          margin: 0;
        }
      }
      
      .user-avatar {
        :deep(.van-image) {
          border: 2px solid rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
  
  .dashboard-stats {
    margin: -16px 16px 24px;
    
    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      
      .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .van-icon {
          font-size: 20px;
          color: white;
        }
      }
      
      .stat-content {
        flex: 1;
        
        .stat-value {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          margin-bottom: 2px;
        }
        
        .stat-label {
          font-size: 12px;
          color: #666;
        }
      }
      
      &--sales .stat-icon {
        background: linear-gradient(135deg, #10B981 0%, #059669 100%);
      }
      
      &--purchase .stat-icon {
        background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
      }
      
      &--retail .stat-icon {
        background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
      }
      
      &--inventory .stat-icon {
        background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
      }
    }
  }
  
  .dashboard-quick-actions,
  .dashboard-notifications {
    margin: 0 16px 24px;
    
    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }
    }
  }
  
  .dashboard-quick-actions {
    :deep(.van-grid-item) {
      .van-grid-item__content {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 16px 8px;
      }
      
      .van-grid-item__icon {
        font-size: 24px;
        color: #3B82F6;
        margin-bottom: 8px;
      }
      
      .van-grid-item__text {
        font-size: 12px;
        color: #333;
      }
      
      .van-badge {
        .van-badge__wrapper {
          .van-badge__content {
            background: #ff4757;
            font-size: 10px;
          }
        }
      }
    }
  }
  
  .notification-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .notification-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background: #f7f8fa;
      }
      
      .notification-content {
        flex: 1;
        
        .notification-title {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 4px;
        }
        
        .notification-desc {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .notification-time {
          font-size: 11px;
          color: #999;
        }
      }
      
      .notification-status {
        margin-left: 8px;
      }
    }
  }
  
  .dashboard-tenant-info {
    margin: 0 16px;
    
    :deep(.van-notice-bar) {
      border-radius: 8px;
      font-size: 12px;
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .mobile-dashboard {
    .dashboard-header {
      padding: 20px 12px 28px;
      
      .user-welcome .welcome-text h2 {
        font-size: 18px;
      }
    }
    
    .dashboard-stats {
      margin: -12px 12px 20px;
      
      .stat-card {
        padding: 12px;
        
        .stat-icon {
          width: 36px;
          height: 36px;
          
          .van-icon {
            font-size: 18px;
          }
        }
        
        .stat-content .stat-value {
          font-size: 16px;
        }
      }
    }
    
    .dashboard-quick-actions,
    .dashboard-notifications {
      margin: 0 12px 20px;
    }
  }
}
</style>
