<template>
  <div class="mobile-entry">
    <div class="entry-container">
      <div class="logo-section">
        <img src="/logo.png" alt="jshERP" class="logo" />
        <h1 class="title">jshERP 移动端</h1>
        <p class="subtitle">专为移动设备优化的ERP系统</p>
      </div>
      
      <div class="feature-section">
        <div class="feature-item">
          <van-icon name="shop-o" size="24" />
          <span>销售管理</span>
        </div>
        <div class="feature-item">
          <van-icon name="goods-collect-o" size="24" />
          <span>库存查询</span>
        </div>
        <div class="feature-item">
          <van-icon name="shopping-cart-o" size="24" />
          <span>采购管理</span>
        </div>
        <div class="feature-item">
          <van-icon name="balance-o" size="24" />
          <span>财务报表</span>
        </div>
      </div>
      
      <div class="action-section">
        <van-button 
          type="primary" 
          size="large" 
          block 
          @click="goToMobileLogin"
          class="enter-btn"
        >
          进入移动端
        </van-button>
        
        <van-button 
          type="default" 
          size="large" 
          block 
          @click="goToDesktop"
          class="desktop-btn"
        >
          使用桌面版
        </van-button>
      </div>
      
      <div class="info-section">
        <p class="info-text">
          <van-icon name="info-o" />
          移动端提供核心功能的便捷访问，完整功能请使用桌面版
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileEntry',
  methods: {
    goToMobileLogin() {
      this.$router.push('/m/auth/login')
    },
    
    goToDesktop() {
      // 跳转到桌面端
      window.location.href = '/'
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-entry {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.entry-container {
  background: white;
  border-radius: 16px;
  padding: 40px 24px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.logo-section {
  margin-bottom: 40px;
  
  .logo {
    width: 80px;
    height: 80px;
    margin-bottom: 16px;
  }
  
  .title {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin: 0 0 8px 0;
  }
  
  .subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

.feature-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 40px;
  
  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
    background: #f7f8fa;
    border-radius: 12px;
    
    .van-icon {
      color: #1989fa;
      margin-bottom: 8px;
    }
    
    span {
      font-size: 12px;
      color: #333;
    }
  }
}

.action-section {
  margin-bottom: 24px;
  
  .enter-btn {
    margin-bottom: 12px;
    height: 48px;
    font-size: 16px;
    font-weight: bold;
  }
  
  .desktop-btn {
    height: 44px;
    font-size: 14px;
  }
}

.info-section {
  .info-text {
    font-size: 12px;
    color: #999;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .van-icon {
      margin-right: 4px;
    }
  }
}
</style>
