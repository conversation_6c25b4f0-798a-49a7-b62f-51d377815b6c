/**
 * 移动端认证相关路由
 */

export default [
  {
    path: 'login',
    name: 'MobileLogin',
    component: () => import('@/mobile/views/auth/Login.vue'),
    meta: {
      title: '登录',
      showHeader: false,
      showTabBar: false,
      keepAlive: false
    }
  },
  {
    path: 'register',
    name: 'MobileRegister',
    component: () => import('@/mobile/views/auth/Register.vue'),
    meta: {
      title: '注册',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false
    }
  },
  {
    path: 'forgot-password',
    name: 'MobileForgotPassword',
    component: () => import('@/mobile/views/auth/ForgotPassword.vue'),
    meta: {
      title: '忘记密码',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false
    }
  },
  {
    path: 'reset-password',
    name: 'MobileResetPassword',
    component: () => import('@/mobile/views/auth/ResetPassword.vue'),
    meta: {
      title: '重置密码',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false
    }
  }
]
