/**
 * 移动端采购管理路由
 */

export default [
  {
    path: 'purchase',
    name: 'MobilePurchaseList',
    component: () => import('@/mobile/views/purchase/PurchaseList.vue'),
    meta: {
      title: '采购管理',
      icon: 'logistics',
      tab: 'purchase',
      showHeader: true,
      showTabBar: true,
      keepAlive: true,
      requiresAuth: true,
      permission: 'purchase:view'
    }
  },
  {
    path: 'purchase/orders',
    name: 'MobilePurchaseOrderList',
    component: () => import('@/mobile/views/purchase/PurchaseOrderList.vue'),
    meta: {
      title: '采购订单',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: true,
      permission: 'purchase:view'
    }
  },
  {
    path: 'purchase/orders/create',
    name: 'MobilePurchaseOrderCreate',
    component: () => import('@/mobile/views/purchase/PurchaseOrderCreate.vue'),
    meta: {
      title: '新增采购订单',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'purchase:add'
    }
  },
  {
    path: 'purchase/orders/:id',
    name: 'MobilePurchaseOrderDetail',
    component: () => import('@/mobile/views/purchase/PurchaseOrderDetail.vue'),
    meta: {
      title: '采购订单详情',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'purchase:view'
    }
  },
  {
    path: 'purchase/suppliers',
    name: 'MobileSupplierList',
    component: () => import('@/mobile/views/purchase/SupplierList.vue'),
    meta: {
      title: '供应商管理',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: true,
      permission: 'supplier:view'
    }
  },
  {
    path: 'purchase/suppliers/:id',
    name: 'MobileSupplierDetail',
    component: () => import('@/mobile/views/purchase/SupplierDetail.vue'),
    meta: {
      title: '供应商详情',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'supplier:view'
    }
  },
  {
    path: 'purchase/apply',
    name: 'MobilePurchaseApply',
    component: () => import('@/mobile/views/purchase/PurchaseApply.vue'),
    meta: {
      title: '采购申请',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'purchase:apply'
    }
  }
]
