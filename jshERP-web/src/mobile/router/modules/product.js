/**
 * 移动端商品管理路由
 */

export default [
  {
    path: 'products',
    name: 'MobileProductList',
    component: () => import('@/mobile/views/product/ProductList.vue'),
    meta: {
      title: '商品管理',
      icon: 'goods-collect-o',
      tab: 'products',
      showHeader: true,
      showTabBar: true,
      keepAlive: true,
      requiresAuth: true,
      permission: 'product:view'
    }
  },
  {
    path: 'products/search',
    name: 'MobileProductSearch',
    component: () => import('@/mobile/views/product/ProductSearch.vue'),
    meta: {
      title: '商品搜索',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'product:view'
    }
  },
  {
    path: 'products/scan',
    name: 'MobileProductScan',
    component: () => import('@/mobile/views/product/ProductScan.vue'),
    meta: {
      title: '扫码查询',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'product:view'
    }
  },
  {
    path: 'products/:id',
    name: 'MobileProductDetail',
    component: () => import('@/mobile/views/product/ProductDetail.vue'),
    meta: {
      title: '商品详情',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'product:view'
    }
  },
  {
    path: 'products/create',
    name: 'MobileProductCreate',
    component: () => import('@/mobile/views/product/ProductCreate.vue'),
    meta: {
      title: '新增商品',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'product:add'
    }
  },
  {
    path: 'products/:id/edit',
    name: 'MobileProductEdit',
    component: () => import('@/mobile/views/product/ProductEdit.vue'),
    meta: {
      title: '编辑商品',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'product:edit'
    }
  },
  {
    path: 'categories',
    name: 'MobileCategoryList',
    component: () => import('@/mobile/views/product/CategoryList.vue'),
    meta: {
      title: '商品分类',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: true,
      permission: 'product:view'
    }
  },
  {
    path: 'categories/:id/products',
    name: 'MobileCategoryProducts',
    component: () => import('@/mobile/views/product/CategoryProducts.vue'),
    meta: {
      title: '分类商品',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'product:view'
    }
  }
]
