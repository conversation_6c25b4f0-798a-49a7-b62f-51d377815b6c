/**
 * 移动端销售管理路由
 */

export default [
  {
    path: 'sales',
    name: 'MobileSalesList',
    component: () => import('@/mobile/views/sales/SalesList.vue'),
    meta: {
      title: '销售管理',
      icon: 'shopping-cart-o',
      tab: 'sales',
      showHeader: true,
      showTabBar: true,
      keepAlive: true,
      requiresAuth: true,
      permission: 'sales:view'
    }
  },
  {
    path: 'sales/orders',
    name: 'MobileOrderList',
    component: () => import('@/mobile/views/sales/OrderList.vue'),
    meta: {
      title: '销售订单',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: true,
      permission: 'sales:view'
    }
  },
  {
    path: 'sales/orders/create',
    name: 'MobileOrderCreate',
    component: () => import('@/mobile/views/sales/OrderCreate.vue'),
    meta: {
      title: '新增订单',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'sales:add'
    }
  },
  {
    path: 'sales/orders/:id',
    name: 'MobileOrderDetail',
    component: () => import('@/mobile/views/sales/OrderDetail.vue'),
    meta: {
      title: '订单详情',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'sales:view'
    }
  },
  {
    path: 'sales/orders/:id/edit',
    name: 'MobileOrderEdit',
    component: () => import('@/mobile/views/sales/OrderEdit.vue'),
    meta: {
      title: '编辑订单',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'sales:edit'
    }
  },
  {
    path: 'sales/customers',
    name: 'MobileCustomerList',
    component: () => import('@/mobile/views/sales/CustomerList.vue'),
    meta: {
      title: '客户管理',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: true,
      permission: 'customer:view'
    }
  },
  {
    path: 'sales/customers/:id',
    name: 'MobileCustomerDetail',
    component: () => import('@/mobile/views/sales/CustomerDetail.vue'),
    meta: {
      title: '客户详情',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'customer:view'
    }
  },
  {
    path: 'sales/retail',
    name: 'MobileRetailSale',
    component: () => import('@/mobile/views/sales/RetailSale.vue'),
    meta: {
      title: '零售销售',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'sales:retail'
    }
  }
]
