/**
 * 移动端路由配置
 * 基于 /m 前缀的移动端专用路由系统
 */

import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '@/store'

// 移动端布局组件
import MobileLayout from '@/mobile/components/layout/MobileLayout.vue'
import MobileAuthLayout from '@/mobile/components/layout/MobileAuthLayout.vue'

// 路由模块
import authRoutes from './modules/auth'
import dashboardRoutes from './modules/dashboard'
import productRoutes from './modules/product'
import salesRoutes from './modules/sales'
import inventoryRoutes from './modules/inventory'
import purchaseRoutes from './modules/purchase'
import financeRoutes from './modules/finance'
import profileRoutes from './modules/profile'

Vue.use(VueRouter)

/**
 * 移动端路由配置
 */
export const mobileRoutes = [
  // 移动端认证路由（无需布局）
  {
    path: '/m/auth',
    component: MobileAuthLayout,
    redirect: '/m/auth/login',
    children: authRoutes
  },
  
  // 移动端主应用路由
  {
    path: '/m',
    component: MobileLayout,
    redirect: '/m/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      ...dashboardRoutes,
      ...productRoutes,
      ...salesRoutes,
      ...inventoryRoutes,
      ...purchaseRoutes,
      ...financeRoutes,
      ...profileRoutes
    ]
  },
  
  // 移动端404页面
  {
    path: '/m/404',
    name: 'Mobile404',
    component: () => import('@/mobile/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      showTabBar: false
    }
  },
  
  // 移动端错误页面重定向
  {
    path: '/m/*',
    redirect: '/m/404'
  }
]

// 导出路由配置供主路由使用
// 移动端路由守卫逻辑将在主路由中统一处理
