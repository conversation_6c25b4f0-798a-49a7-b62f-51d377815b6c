/**
 * 移动端路由配置
 * 基于 /m 前缀的移动端专用路由系统
 */

import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '@/store'

// 移动端布局组件
import MobileLayout from '@/mobile/components/layout/MobileLayout.vue'
import MobileAuthLayout from '@/mobile/components/layout/MobileAuthLayout.vue'

// 路由模块
import authRoutes from './modules/auth'
import dashboardRoutes from './modules/dashboard'
import productRoutes from './modules/product'
import salesRoutes from './modules/sales'
import inventoryRoutes from './modules/inventory'
import purchaseRoutes from './modules/purchase'
import financeRoutes from './modules/finance'
import profileRoutes from './modules/profile'

Vue.use(VueRouter)

/**
 * 移动端路由配置
 */
export const mobileRoutes = [
  // 移动端认证路由（无需布局）
  {
    path: '/m/auth',
    component: MobileAuthLayout,
    redirect: '/m/auth/login',
    children: authRoutes
  },
  
  // 移动端主应用路由
  {
    path: '/m',
    component: MobileLayout,
    redirect: '/m/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      ...dashboardRoutes,
      ...productRoutes,
      ...salesRoutes,
      ...inventoryRoutes,
      ...purchaseRoutes,
      ...financeRoutes,
      ...profileRoutes
    ]
  },
  
  // 移动端404页面
  {
    path: '/m/404',
    name: 'Mobile404',
    component: () => import('@/mobile/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      showTabBar: false
    }
  },
  
  // 移动端错误页面重定向
  {
    path: '/m/*',
    redirect: '/m/404'
  }
]

/**
 * 创建移动端路由实例
 */
const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes: mobileRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})

/**
 * 移动端路由守卫
 */
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - jshERP Mobile`
  }
  
  // 检查认证状态
  if (to.meta.requiresAuth) {
    const token = store.getters['user/token']
    
    if (!token) {
      // 未登录，跳转到登录页
      next({
        path: '/m/auth/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 检查用户信息是否已加载
    if (!store.getters['user/userInfo']) {
      try {
        await store.dispatch('user/getUserInfo')
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 清除无效token
        store.dispatch('user/logout')
        next({
          path: '/m/auth/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
  }
  
  // 检查权限
  if (to.meta.permission) {
    const hasPermission = store.getters['user/hasPermission'](to.meta.permission)
    if (!hasPermission) {
      next('/m/403')
      return
    }
  }
  
  next()
})

/**
 * 路由错误处理
 */
router.onError((error) => {
  console.error('移动端路由错误:', error)
  
  // 网络错误处理
  if (error.message.includes('Loading chunk')) {
    window.location.reload()
  }
})

export default router
