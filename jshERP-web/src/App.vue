<template>
  <a-config-provider :locale="locale">
    <div id="app" :class="appClass">
      <!-- 移动端布局 -->
      <router-view v-if="isMobile" />
      <!-- 桌面端布局 -->
      <router-view v-else />

      <!-- stagewise-toolbar 暂时禁用以解决依赖冲突 -->
      <!-- <stagewise-toolbar v-if="isDev" :config="stagewiseConfig" /> -->
    </div>
  </a-config-provider>
</template>
<script>
  // import { StagewiseToolbar } from '@stagewise/toolbar-vue'
  // import { VuePlugin } from '@stagewise-plugins/vue'
  import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
  import enquireScreen from '@/utils/device'
  import { detectDevice } from '@/shared/utils/device'

  export default {
    name: 'App',
    components: {
      // StagewiseToolbar
    },
    data () {
      return {
        locale: zhCN,
        isDev: process.env.NODE_ENV === 'development',
        device: detectDevice()
        // stagewiseConfig: {
        //   plugins: [VuePlugin()]
        // }
      }
    },
    computed: {
      isMobile() {
        return this.device.isMobile
      },
      appClass() {
        return {
          'mobile-app': this.isMobile,
          'desktop-app': !this.isMobile
        }
      }
    },
    created () {
      // 设备切换时的路由处理
      this.handleDeviceRoute()

      // 保持原有的设备检测逻辑（用于桌面端布局）
      let that = this
      enquireScreen(deviceType => {
        // tablet
        if (deviceType === 0) {
          that.$store.commit('TOGGLE_DEVICE', 'mobile')
          that.$store.dispatch('setSidebar', false)
        }
        // mobile
        else if (deviceType === 1) {
          that.$store.commit('TOGGLE_DEVICE', 'mobile')
          that.$store.dispatch('setSidebar', false)
        } else {
          that.$store.commit('TOGGLE_DEVICE', 'desktop')
          that.$store.dispatch('setSidebar', true)
        }
      })

      // 监听设备方向变化
      this.setupOrientationListener()
    },
    methods: {
      handleDeviceRoute() {
        const currentPath = this.$route.path

        // 如果是移动设备但访问桌面端路由，重定向到移动端
        if (this.isMobile && !currentPath.startsWith('/m') && currentPath !== '/') {
          const mobilePath = '/m' + (currentPath === '/dashboard/analysis' ? '/dashboard' : currentPath)
          this.$router.replace(mobilePath)
        }
        // 如果是桌面设备但访问移动端路由，重定向到桌面端
        else if (!this.isMobile && currentPath.startsWith('/m')) {
          const desktopPath = currentPath.replace('/m', '') || '/dashboard/analysis'
          this.$router.replace(desktopPath)
        }
      },

      setupOrientationListener() {
        if (this.isMobile) {
          // 监听设备方向变化
          const handleOrientationChange = () => {
            setTimeout(() => {
              // 重新检测设备信息
              this.device = detectDevice()
              // 可以在这里处理方向变化的逻辑
              this.$emit('orientation-change', this.device)
            }, 100)
          }

          window.addEventListener('orientationchange', handleOrientationChange)
          window.addEventListener('resize', handleOrientationChange)

          // 组件销毁时清理监听器
          this.$once('hook:beforeDestroy', () => {
            window.removeEventListener('orientationchange', handleOrientationChange)
            window.removeEventListener('resize', handleOrientationChange)
          })
        }
      }
    },

    watch: {
      // 监听路由变化，确保设备类型匹配
      '$route'(to, from) {
        this.handleDeviceRoute()
      }
    }
  }
</script>
<style lang="less">
#app {
  height: 100%;

  // 桌面端样式
  &.desktop-app {
    font-size: 14px;
    line-height: 1.5;
    // 保持原有桌面端样式
  }

  // 移动端样式
  &.mobile-app {
    font-size: 14px;
    line-height: 1.4;

    // 移动端特有的全局样式
    * {
      box-sizing: border-box;
      -webkit-tap-highlight-color: transparent;
    }

    // 移动端滚动优化
    .mobile-scroll {
      -webkit-overflow-scrolling: touch;
      overflow-scrolling: touch;
    }

    // 禁用选择和拖拽
    img {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
      user-drag: none;
    }

    // 移除点击高亮
    * {
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
    }

    // 移动端字体优化
    body {
      -webkit-text-size-adjust: 100%;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }
}

// 移动端和桌面端的过渡动画
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style>