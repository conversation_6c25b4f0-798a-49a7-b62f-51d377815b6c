/**
 * 统一路由管理
 * 整合桌面端和移动端路由系统
 */

import Vue from 'vue'
import Router from 'vue-router'
import { detectDevice } from '@/shared/utils/device'

// 桌面端路由
import { constantRouterMap } from '@/config/router.config'

// 移动端路由
import mobileRouter from '@/mobile/router'

//update-begin-author:taoyan date:20191011 for:TASK #3214 【优化】访问online功能测试 浏览器控制台抛出异常
try {
  const originalPush = Router.prototype.push
  Router.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err)
  }
} catch (e) {
}
//update-end-author:taoyan date:20191011 for:TASK #3214 【优化】访问online功能测试 浏览器控制台抛出异常

Vue.use(Router)

// 设备检测
const device = detectDevice()

/**
 * 统一路由配置
 * 包含桌面端和移动端的所有路由
 */
const allRoutes = [
  // 移动端路由（优先级更高，避免被桌面端路由覆盖）
  ...mobileRouter.options.routes,

  // 桌面端路由
  ...constantRouterMap,

  // 根路径重定向
  {
    path: '/',
    redirect: () => {
      // 根据设备类型自动重定向
      return device.isMobile ? '/m/dashboard' : '/dashboard/analysis'
    }
  },

  // 404页面
  {
    path: '/404',
    component: () => import('@/views/exception/404')
  },

  // 移动端404重定向
  {
    path: '/m/404',
    component: () => import('@/mobile/views/error/404')
  },

  // 通配符路由（必须放在最后）
  {
    path: '*',
    redirect: (to) => {
      // 根据路径判断重定向到对应的404页面
      if (to.path.startsWith('/m/')) {
        return '/m/404'
      }
      return '/404'
    }
  }
]

/**
 * 创建路由实例
 */
const router = new Router({
  mode: 'history',
  base: process.env.BASE_URL,
  scrollBehavior: (to, from, savedPosition) => {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  },
  routes: allRoutes
})

/**
 * 全局路由守卫
 */
router.beforeEach((to, from, next) => {
  // 设备检测和路由重定向
  const currentDevice = detectDevice()
  const isMobilePath = to.path.startsWith('/m/')

  // 如果是移动设备但访问桌面端路由，重定向到移动端
  if (currentDevice.isMobile && !isMobilePath && to.path !== '/') {
    const mobilePath = '/m' + to.path
    next(mobilePath)
    return
  }

  // 如果是桌面设备但访问移动端路由，重定向到桌面端
  if (!currentDevice.isMobile && isMobilePath) {
    const desktopPath = to.path.replace('/m', '') || '/dashboard/analysis'
    next(desktopPath)
    return
  }

  next()
})

/**
 * 路由错误处理
 */
router.onError((error) => {
  console.error('路由错误:', error)

  // 处理代码分割加载失败
  if (error.message.includes('Loading chunk')) {
    window.location.reload()
  }
})

export default router