<template>
  <a-row :gutter="24">
    <a-col :span="24">
      <a-card :bordered="false" class="inventory-check-card">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :lg="6" :md="8" :sm="24">
                <a-form-item label="单据编号">
                  <a-input
                    v-model="queryParam.number"
                    placeholder="请输入单据编号"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :lg="6" :md="8" :sm="24">
                <a-form-item label="商品信息">
                  <a-input
                    v-model="queryParam.materialParam"
                    placeholder="请输入条码、名称、助记码、规格、型号等信息"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :lg="6" :md="8" :sm="24">
                <a-form-item label="仓库">
                  <a-select
                    v-model="queryParam.depotId"
                    placeholder="请选择仓库"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option v-for="depot in depotList" :key="depot.id" :value="depot.id">
                      {{ depot.depotName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :lg="6" :md="8" :sm="24">
                <a-form-item label="操作员">
                  <a-select
                    v-model="queryParam.creator"
                    placeholder="请选择操作员"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option v-for="user in userList" :key="user.id" :value="user.id">
                      {{ user.userName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :lg="6" :md="8" :sm="24">
                <a-form-item label="单据日期">
                  <a-range-picker
                    v-model="queryParam.dateRange"
                    style="width: 100%"
                    format="YYYY-MM-DD"
                    :placeholder="['开始时间', '结束时间']"
                  />
                </a-form-item>
              </a-col>
              <a-col :lg="6" :md="8" :sm="24">
                <span class="table-page-search-submitButtons">
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                  <a-button @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button type="primary" icon="plus" @click="handleAdd">新增</a-button>
          <a-button 
            type="danger" 
            icon="delete" 
            @click="batchDel" 
            :disabled="selectedRowKeys.length <= 0"
          >
            删除
          </a-button>
          <a-button icon="reload" @click="loadData">刷新</a-button>
          <a-button icon="download" @click="handleExportAll">全部导出</a-button>
          <a-button icon="printer" @click="handlePrint">打印</a-button>
        </div>

        <!-- 数据表格 -->
        <div>
          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
            @change="handleTableChange"
          >
            <!-- 操作列 -->
            <span slot="action" slot-scope="text, record">
              <a @click="handleDetail(record)">查看</a>
              <a-divider type="vertical" />
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a @click="handleDelete(record)">删除</a>
            </span>

            <!-- 状态列 -->
            <template slot="customRenderStatus" slot-scope="status">
              <a-tag v-if="status == '0'" color="orange">草稿</a-tag>
              <a-tag v-if="status == '1'" color="blue">盘点中</a-tag>
              <a-tag v-if="status == '2'" color="green">已完成</a-tag>
            </template>
          </a-table>
        </div>

        <!-- 弹窗组件 -->
        <inventory-check-modal ref="modalForm" @ok="modalFormOk" @close="modalFormClose" />
        <import-stock-modal ref="importModal" @ok="modalFormOk" @close="modalFormClose" />
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import InventoryCheckModal from './modules/InventoryCheckModal'
import ImportStockModal from './modules/ImportStockModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, deleteAction, putAction } from '@/api/manage'

export default {
  name: 'InventoryCheckList',
  mixins: [JeecgListMixin],
  components: {
    InventoryCheckModal,
    ImportStockModal
  },
  data() {
    return {
      // 查询条件
      queryParam: {
        number: '',
        beginTime: '',
        endTime: '',
        type: '其它',
        subType: '盘点'
      },
      // 表格列配置
      columns: [
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'action' }
        },
        {
          title: '申请编号',
          dataIndex: 'number',
          width: 150
        },
        {
          title: '关联单据',
          dataIndex: 'linkNumber',
          width: 150
        },
        {
          title: '商品信息',
          dataIndex: 'materialParam',
          width: 200
        },
        {
          title: '申请日期',
          dataIndex: 'operTime',
          width: 150
        },
        {
          title: '操作员',
          dataIndex: 'operPersonName',
          width: 100
        },
        {
          title: '数量',
          dataIndex: 'totalNum',
          width: 100,
          align: 'right'
        },
        {
          title: '金额合计',
          dataIndex: 'totalPrice',
          width: 120,
          align: 'right'
        },
        {
          title: '单据日期',
          dataIndex: 'operTime',
          width: 150
        },
        {
          title: '操作员',
          dataIndex: 'operPersonName',
          width: 100
        },
        {
          title: '备注',
          dataIndex: 'remark',
          width: 200
        },
        {
          title: '金额合计',
          dataIndex: 'totalPrice',
          width: 120,
          align: 'right'
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'customRenderStatus' }
        }
      ],
      // API配置
      url: {
        list: '/depotHead/list',
        delete: '/depotHead/delete',
        deleteBatch: '/depotHead/deleteBatch'
      }
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    // 新增
    handleAdd() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '新增盘点单'
    },
    
    // 编辑
    handleEdit(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑盘点单'
    },
    
    // 查看详情
    handleDetail(record) {
      this.$router.push({
        path: '/inventory/inventory-input-list',
        query: { id: record.id, mode: 'view' }
      })
    },
    
    // 导入
    handleImport() {
      this.$refs.importModal.show()
    },
    
    // 导出全部
    handleExportAll() {
      // TODO: 实现导出功能
      this.$message.info('导出功能开发中...')
    },
    
    // 打印
    handlePrint() {
      // TODO: 实现打印功能
      this.$message.info('打印功能开发中...')
    }
  }
}
</script>

<style lang="less" scoped>
.inventory-check-card {
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .table-page-search-wrapper {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }
  
  .table-operator {
    margin-bottom: 16px;
    
    .ant-btn {
      margin-right: 8px;
      border-radius: 4px;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
