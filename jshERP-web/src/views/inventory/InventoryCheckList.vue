<template>
  <a-row :gutter="24">
    <a-col :span="24">
      <a-card :bordered="false" class="inventory-check-card">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :lg="6" :md="8" :sm="24">
                <a-form-item label="单据编号">
                  <a-input
                    v-model="queryParam.number"
                    placeholder="请输入单据编号"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :lg="6" :md="8" :sm="24">
                <a-form-item label="商品信息">
                  <a-input
                    v-model="queryParam.materialParam"
                    placeholder="请输入条码、名称、助记码、规格、型号等信息"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :lg="6" :md="8" :sm="24">
                <a-form-item label="仓库">
                  <a-select
                    v-model="queryParam.depotId"
                    placeholder="请选择仓库"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option v-for="depot in depotList" :key="depot.id" :value="depot.id">
                      {{ depot.depotName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :lg="6" :md="8" :sm="24">
                <a-form-item label="操作员">
                  <a-select
                    v-model="queryParam.creator"
                    placeholder="请选择操作员"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option v-for="user in userList" :key="user.id" :value="user.id">
                      {{ user.userName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :lg="6" :md="8" :sm="24">
                <a-form-item label="单据日期">
                  <a-range-picker
                    v-model="queryParam.dateRange"
                    style="width: 100%"
                    format="YYYY-MM-DD"
                    :placeholder="['开始时间', '结束时间']"
                  />
                </a-form-item>
              </a-col>
              <a-col :lg="6" :md="8" :sm="24">
                <span class="table-page-search-submitButtons">
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                  <a-button @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- 操作按钮区域 -->
        <div class="table-operator" style="margin-top: 5px">
          <a-button v-if="btnEnableList.indexOf(1)>-1" @click="myHandleAdd" type="primary" icon="plus">新增</a-button>
          <a-button v-if="btnEnableList.indexOf(1)>-1" icon="delete" @click="batchDel">删除</a-button>
          <a-button v-if="checkFlag && btnEnableList.indexOf(2)>-1" icon="check" @click="batchSetStatus(1)">审核</a-button>
          <a-button v-if="checkFlag && btnEnableList.indexOf(7)>-1" icon="stop" @click="batchSetStatus(0)">反审核</a-button>
          <a-button v-if="isShowExcel && btnEnableList.indexOf(3)>-1" icon="download" @click="handleExport">导出</a-button>
          <a-button icon="printer" @click="handlePrint">打印</a-button>
        </div>

        <!-- 数据表格 -->
        <div>
          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
            @change="handleTableChange"
          >
            <!-- 操作列 -->
            <span slot="action" slot-scope="text, record">
              <a @click="myHandleDetail(record, '盘点复盘', prefixNo)">查看</a>
              <a-divider v-if="btnEnableList.indexOf(1)>-1" type="vertical" />
              <a v-if="btnEnableList.indexOf(1)>-1" @click="myHandleEdit(record)">编辑</a>
              <a-divider v-if="btnEnableList.indexOf(1)>-1" type="vertical" />
              <a-popconfirm v-if="btnEnableList.indexOf(1)>-1" title="确定删除吗?" @confirm="() => myHandleDelete(record)">
                <a>删除</a>
              </a-popconfirm>
            </span>

            <!-- 状态列 -->
            <template slot="customRenderStatus" slot-scope="status">
              <a-tag v-if="status == '0'" color="orange">草稿</a-tag>
              <a-tag v-if="status == '1'" color="blue">盘点中</a-tag>
              <a-tag v-if="status == '2'" color="green">已完成</a-tag>
            </template>
          </a-table>
        </div>

        <!-- 弹窗组件 -->
        <inventory-check-modal ref="modalForm" @ok="modalFormOk" @close="modalFormClose" />
        <import-stock-modal ref="importModal" @ok="modalFormOk" @close="modalFormClose" />
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import InventoryCheckModal from './modules/InventoryCheckModalNew'
import ImportStockModal from './modules/ImportStockModal'
import { BillListMixin } from '@/views/bill/mixins/BillListMixin'

export default {
  name: 'InventoryCheckList',
  mixins: [BillListMixin],
  components: {
    InventoryCheckModal,
    ImportStockModal
  },
  data() {
    return {
      // 查询条件
      queryParam: {
        number: '',
        materialParam: '',
        depotId: '',
        creator: '',
        dateRange: [],
        type: '',
        subType: ''
      },
      // API配置
      url: {
        list: '/depotHead/list',
        delete: '/depotHead/delete',
        deleteBatch: '/depotHead/deleteBatch'
      },
      // 单据前缀
      prefixNo: 'PDFP',
      // 仓库列表
      depotList: [],
      // 用户列表
      userList: [],
      // 表格列配置
      columns: [
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'action' }
        },
        {
          title: '申请编号',
          dataIndex: 'number',
          width: 150
        },
        {
          title: '关联单据',
          dataIndex: 'linkNumber',
          width: 150
        },
        {
          title: '商品信息',
          dataIndex: 'materialParam',
          width: 200
        },
        {
          title: '申请日期',
          dataIndex: 'operTime',
          width: 150
        },
        {
          title: '操作员',
          dataIndex: 'operPersonName',
          width: 100
        },
        {
          title: '数量',
          dataIndex: 'totalNum',
          width: 100,
          align: 'right'
        },
        {
          title: '金额合计',
          dataIndex: 'totalPrice',
          width: 120,
          align: 'right'
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'customRenderStatus' }
        }
      ],
      // API配置
      url: {
        list: '/depotHead/list',
        delete: '/depotHead/delete',
        deleteBatch: '/depotHead/deleteBatch'
      }
    }
  },
  created() {
    // 暂时使用mock数据测试界面
    this.loadMockData()
    this.initDepotList()
    this.initUserList()
  },
  methods: {
    // 新增
    myHandleAdd() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '新增'
    },

    // 编辑
    myHandleEdit(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
    },

    // 查看详情
    myHandleDetail(record, title, prefixNo) {
      this.$refs.modalForm.detail(record)
      this.$refs.modalForm.title = '查看详情'
    },

    // 删除
    myHandleDelete(record) {
      this.handleDelete(record.id)
    },

    // 弹窗确定
    modalFormOk() {
      this.loadData()
    },

    // 弹窗关闭
    modalFormClose() {
      // 弹窗关闭处理
    },

    // 初始化仓库列表
    initDepotList() {
      this.getAction('/depot/list').then(res => {
        if (res.success) {
          this.depotList = res.result || []
        }
      })
    },

    // 初始化用户列表
    initUserList() {
      this.getAction('/user/list').then(res => {
        if (res.success) {
          this.userList = res.result || []
        }
      })
    },

    // 打印
    handlePrint() {
      this.$message.info('打印功能开发中...')
    },

    // 加载Mock数据用于测试界面
    loadMockData() {
      this.loading = true
      setTimeout(() => {
        this.dataSource = [
          {
            id: 1,
            number: 'PDFP202412260001',
            linkNumber: 'PDLR202412260001',
            materialParam: '苹果 iPhone 15, 华为 Mate 60',
            operTime: '2024-12-26 10:30:00',
            operPersonName: '张三',
            totalNum: 150,
            totalPrice: 146800.00,
            status: '0'
          },
          {
            id: 2,
            number: 'PDFP202412260002',
            linkNumber: 'PDLR202412260002',
            materialParam: '小米 14 Pro, OPPO Find X7',
            operTime: '2024-12-26 11:15:00',
            operPersonName: '李四',
            totalNum: 120,
            totalPrice: 95600.00,
            status: '1'
          },
          {
            id: 3,
            number: 'PDFP202412260003',
            linkNumber: '',
            materialParam: 'vivo X100, 一加 12',
            operTime: '2024-12-26 14:20:00',
            operPersonName: '王五',
            totalNum: 90,
            totalPrice: 67800.00,
            status: '0'
          }
        ]
        this.ipagination.total = 3
        this.loading = false
      }, 500)
    }
  }
}
</script>

<style lang="less" scoped>
// jshERP标准样式，无需额外样式
</style>
