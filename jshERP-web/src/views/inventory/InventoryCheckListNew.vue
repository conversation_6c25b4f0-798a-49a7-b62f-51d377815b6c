<template>
  <a-row :gutter="24">
    <a-col :md="24">
      <a-card :bordered="false">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="24">
              <a-col :md="6" :sm="24">
                <a-form-item label="申请编号">
                  <a-input placeholder="请输入申请编号" v-model="queryParam.number"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="商品信息">
                  <a-input placeholder="请输入商品信息" v-model="queryParam.materialParam"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item>
                  <a-button type="primary" @click="searchQuery">查询</a-button>
                  <a-button style="margin-left: 8px" @click="searchReset">重置</a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
        
        <!-- 操作按钮区域 -->
        <div class="table-operator" style="margin-top: 5px">
          <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
          <a-button icon="delete" @click="handleDelete">删除</a-button>
          <a-button icon="check" @click="handleAudit">审核</a-button>
          <a-button icon="stop" @click="handleUnaudit">反审核</a-button>
          <a-button icon="download" @click="handleExport">导出</a-button>
          <a-button icon="printer" @click="handlePrint">打印</a-button>
        </div>
        
        <!-- table区域 -->
        <div>
          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="pagination"
            :loading="loading">
            <span slot="action" slot-scope="text, record">
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a @click="handleView(record)">查看</a>
            </span>
            <template slot="customRenderStatus" slot-scope="status">
              <a-tag v-if="status == '0'" color="red">未审核</a-tag>
              <a-tag v-if="status == '1'" color="green">已审核</a-tag>
            </template>
          </a-table>
        </div>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
export default {
  name: 'InventoryCheckListNew',
  data() {
    return {
      // 查询条件
      queryParam: {
        number: '',
        materialParam: ''
      },
      // 表格列配置
      columns: [
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'action' }
        },
        {
          title: '申请编号',
          dataIndex: 'number',
          width: 150
        },
        {
          title: '关联单据',
          dataIndex: 'linkNumber',
          width: 150
        },
        {
          title: '商品信息',
          dataIndex: 'materialParam',
          width: 200
        },
        {
          title: '申请日期',
          dataIndex: 'operTime',
          width: 150
        },
        {
          title: '操作员',
          dataIndex: 'operPersonName',
          width: 100
        },
        {
          title: '数量',
          dataIndex: 'totalNum',
          width: 100,
          align: 'right'
        },
        {
          title: '金额合计',
          dataIndex: 'totalPrice',
          width: 120,
          align: 'right'
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'customRenderStatus' }
        }
      ],
      // 数据源
      dataSource: [],
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 条`
      },
      // 加载状态
      loading: false
    }
  },
  created() {
    this.loadMockData()
  },
  methods: {
    // 查询
    searchQuery() {
      this.loadMockData()
    },
    
    // 重置
    searchReset() {
      this.queryParam.number = ''
      this.queryParam.materialParam = ''
      this.loadMockData()
    },
    
    // 新增
    handleAdd() {
      this.$message.info('新增功能开发中...')
    },
    
    // 编辑
    handleEdit(record) {
      this.$message.info('编辑功能开发中...')
    },
    
    // 查看
    handleView(record) {
      this.$message.info('查看功能开发中...')
    },
    
    // 删除
    handleDelete() {
      this.$message.info('删除功能开发中...')
    },
    
    // 审核
    handleAudit() {
      this.$message.info('审核功能开发中...')
    },
    
    // 反审核
    handleUnaudit() {
      this.$message.info('反审核功能开发中...')
    },
    
    // 导出
    handleExport() {
      this.$message.info('导出功能开发中...')
    },
    
    // 打印
    handlePrint() {
      this.$message.info('打印功能开发中...')
    },
    
    // 加载Mock数据
    loadMockData() {
      this.loading = true
      setTimeout(() => {
        this.dataSource = [
          {
            id: 1,
            number: 'PDFP202412260001',
            linkNumber: 'PDLR202412260001',
            materialParam: '苹果 iPhone 15, 华为 Mate 60',
            operTime: '2024-12-26 10:30:00',
            operPersonName: '张三',
            totalNum: 150,
            totalPrice: 146800.00,
            status: '0'
          },
          {
            id: 2,
            number: 'PDFP202412260002',
            linkNumber: 'PDLR202412260002',
            materialParam: '小米 14 Pro, OPPO Find X7',
            operTime: '2024-12-26 11:15:00',
            operPersonName: '李四',
            totalNum: 120,
            totalPrice: 95600.00,
            status: '1'
          },
          {
            id: 3,
            number: 'PDFP202412260003',
            linkNumber: '',
            materialParam: 'vivo X100, 一加 12',
            operTime: '2024-12-26 14:20:00',
            operPersonName: '王五',
            totalNum: 90,
            totalPrice: 67800.00,
            status: '0'
          }
        ]
        this.pagination.total = 3
        this.loading = false
      }, 500)
    }
  }
}
</script>
