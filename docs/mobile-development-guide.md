# jshERP移动端开发指南

## 📋 项目概述

### 项目背景
基于jshERP桌面端系统，采用完整复制方案开发移动端版本，实现：
- 🎯 **完整ERP功能**：100%保留桌面端所有业务功能
- 🔄 **独立部署**：移动端和桌面端完全隔离部署
- 🔗 **共享后端**：使用相同的API和数据库
- 📱 **移动优化**：针对移动设备的交互和体验优化

### 技术架构
```
┌─────────────────┬─────────────────┐
│   桌面端        │    移动端       │
│  (Vue 2.7.16)   │  (Vue 2.7.16)   │
│  Ant Design     │  Ant Design     │
│  localhost:8080 │  localhost:8081 │
└─────────────────┴─────────────────┘
         │                 │
         └─────────┬───────┘
                   │
         ┌─────────▼─────────┐
         │    共享后端       │
         │  Spring Boot      │
         │  localhost:9999   │
         └─────────┬─────────┘
                   │
         ┌─────────▼─────────┐
         │    MySQL数据库    │
         │  localhost:3306   │
         └───────────────────┘
```

### 开发环境要求
- **Node.js**: 16.x 或更高版本
- **npm**: 8.x 或更高版本
- **Vue CLI**: 4.x 或更高版本
- **操作系统**: macOS/Linux/Windows
- **浏览器**: Chrome 90+, Safari 14+, Firefox 88+

## 🚀 环境搭建

### 1. 项目初始化
```bash
# 1. 复制桌面端项目
cp -r jshERP-web jshERP-mobile

# 2. 进入移动端项目目录
cd jshERP-mobile

# 3. 修改项目配置
npm run setup:mobile
```

### 2. 依赖安装
```bash
# 安装依赖
npm install

# 安装移动端专用依赖
npm install --save-dev @vue/cli-plugin-pwa
npm install --save vue-touch@next
npm install --save better-scroll
```

### 3. 开发服务器启动
```bash
# 启动移动端开发服务器
npm run serve:mobile

# 访问地址
# 本地: http://localhost:8081
# 局域网: http://[your-ip]:8081
```

## 📐 技术规范

### 代码规范
```javascript
// 1. 文件命名：kebab-case
mobile-adapter.js
mobile-table.vue
mobile-form-item.vue

// 2. 组件命名：PascalCase
export default {
  name: 'MobileTable',
  // ...
}

// 3. 变量命名：camelCase
const isMobileDevice = true;
const mobileConfig = {};

// 4. 常量命名：UPPER_SNAKE_CASE
const MOBILE_BREAKPOINT = 768;
const TOUCH_TARGET_SIZE = 44;
```

### 文件组织规范
```
src/
├── api/                    # API调用（从桌面端同步）
├── utils/                  # 工具函数（从桌面端同步）
├── mixins/                 # 混入（从桌面端同步）
├── components/             # 移动端专用组件
│   ├── common/            # 通用组件
│   ├── layout/            # 布局组件
│   └── business/          # 业务组件
├── views/                  # 移动端页面
│   ├── material/          # 商品管理
│   ├── order/             # 订单管理
│   ├── inventory/         # 库存管理
│   └── system/            # 系统管理
├── styles/                 # 移动端样式
│   ├── mobile-base.less   # 基础样式
│   ├── mobile-components.less # 组件样式
│   └── mobile-responsive.less # 响应式样式
└── config/                 # 移动端配置
    ├── mobile-config.js   # 移动端配置
    └── device-config.js   # 设备配置
```

### Git工作流
```bash
# 1. 功能分支开发
git checkout -b feature/mobile-table-adapter

# 2. 提交规范
git commit -m "feat(mobile): add table mobile adapter"
git commit -m "fix(mobile): resolve touch event issue"
git commit -m "style(mobile): update mobile button styles"

# 3. 合并到主分支
git checkout main
git merge feature/mobile-table-adapter
```

## 🔄 代码同步系统

### 同步配置
```javascript
// scripts/sync-config.js
module.exports = {
  // 需要同步的目录
  syncDirs: ['api', 'utils', 'mixins'],
  
  // 排除的文件
  excludeFiles: ['.DS_Store', 'node_modules', '*.log'],
  
  // 备份配置
  backup: {
    enabled: true,
    maxBackups: 10,
    backupDir: './backups'
  },
  
  // 同步策略
  strategy: 'incremental', // 'full' | 'incremental'
  
  // 冲突处理
  conflictResolution: 'desktop-wins' // 'desktop-wins' | 'manual'
};
```

### 同步操作
```bash
# 1. 检查同步状态
npm run sync:check

# 2. 执行同步
npm run sync:from-desktop

# 3. 查看同步日志
npm run sync:log

# 4. 回滚同步
npm run sync:rollback [backup-id]
```

## 📱 移动端适配系统

### 设备检测
```javascript
// src/utils/mobile-adapter.js
class MobileAdapter {
  constructor() {
    this.isMobile = this.detectMobile();
    this.deviceType = this.getDeviceType();
    this.screenSize = this.getScreenSize();
    this.touchSupport = this.detectTouch();
  }

  detectMobile() {
    const userAgent = navigator.userAgent;
    const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
    return mobileRegex.test(userAgent) || window.innerWidth <= 768;
  }

  getDeviceType() {
    const userAgent = navigator.userAgent;
    if (/iPad/.test(userAgent)) return 'tablet';
    if (/iPhone|iPod/.test(userAgent)) return 'phone';
    if (/Android/.test(userAgent)) {
      return window.innerWidth > 600 ? 'tablet' : 'phone';
    }
    return 'desktop';
  }

  getScreenSize() {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
      ratio: window.devicePixelRatio || 1
    };
  }

  detectTouch() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }
}

export default new MobileAdapter();
```

### 组件适配Mixin
```javascript
// src/mixins/mobile-component-mixin.js
import MobileAdapter from '@/utils/mobile-adapter';

export const MobileComponentMixin = {
  data() {
    return {
      isMobile: MobileAdapter.isMobile,
      deviceType: MobileAdapter.deviceType,
      mobileConfig: {}
    };
  },

  created() {
    this.initMobileConfig();
    this.bindResizeEvent();
  },

  beforeDestroy() {
    this.unbindResizeEvent();
  },

  methods: {
    initMobileConfig() {
      this.mobileConfig = this.getMobileConfig();
    },

    getMobileConfig() {
      return {
        table: {
          size: this.isMobile ? 'small' : 'default',
          scroll: this.isMobile ? { x: true } : {},
          pagination: this.isMobile 
            ? { pageSize: 10, simple: true }
            : { pageSize: 20 }
        },
        form: {
          layout: this.isMobile ? 'vertical' : 'horizontal',
          labelCol: this.isMobile ? { span: 24 } : { span: 6 },
          wrapperCol: this.isMobile ? { span: 24 } : { span: 18 }
        }
      };
    },

    bindResizeEvent() {
      window.addEventListener('resize', this.handleResize);
    },

    unbindResizeEvent() {
      window.removeEventListener('resize', this.handleResize);
    },

    handleResize() {
      this.isMobile = MobileAdapter.detectMobile();
      this.deviceType = MobileAdapter.getDeviceType();
      this.initMobileConfig();
    }
  }
};
```

## 🎨 样式系统

### 基础变量
```less
// src/styles/mobile-variables.less
// 移动端主色调
@mobile-primary-color: #1890ff;
@mobile-success-color: #52c41a;
@mobile-warning-color: #faad14;
@mobile-error-color: #f5222d;

// 移动端字体
@mobile-font-size-xs: 10px;
@mobile-font-size-sm: 12px;
@mobile-font-size-base: 14px;
@mobile-font-size-lg: 16px;
@mobile-font-size-xl: 18px;

// 移动端间距
@mobile-spacing-xs: 4px;
@mobile-spacing-sm: 8px;
@mobile-spacing-md: 16px;
@mobile-spacing-lg: 24px;
@mobile-spacing-xl: 32px;

// 移动端触摸目标
@mobile-touch-target-min: 44px;
@mobile-button-height: 44px;
@mobile-input-height: 44px;

// 移动端断点
@mobile-breakpoint-xs: 480px;
@mobile-breakpoint-sm: 576px;
@mobile-breakpoint-md: 768px;
@mobile-breakpoint-lg: 992px;
```

### 响应式设计
```less
// src/styles/mobile-responsive.less
// 移动端响应式混入
.mobile-responsive(@rules) {
  @media (max-width: @mobile-breakpoint-md) {
    @rules();
  }
}

.tablet-responsive(@rules) {
  @media (min-width: @mobile-breakpoint-md) and (max-width: @mobile-breakpoint-lg) {
    @rules();
  }
}

// 使用示例
.my-component {
  padding: @mobile-spacing-lg;
  
  .mobile-responsive({
    padding: @mobile-spacing-md;
    font-size: @mobile-font-size-sm;
  });
  
  .tablet-responsive({
    padding: @mobile-spacing-lg;
    font-size: @mobile-font-size-base;
  });
}
```
