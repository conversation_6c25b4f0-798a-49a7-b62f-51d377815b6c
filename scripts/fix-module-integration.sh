#!/bin/bash

# =====================================================
# jshERP模块集成问题快速修复脚本
# 版本: 1.0
# 日期: 2025-06-26
# 用途: 快速解决jshERP新模块无法访问的问题
# =====================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker环境
check_docker_environment() {
    log_info "检查Docker环境..."
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose未安装或不在PATH中"
        exit 1
    fi
    
    if ! docker-compose ps | grep -q "jsherp-mysql"; then
        log_error "jshERP MySQL容器未运行"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 数据库状态刷新
refresh_database_state() {
    log_info "执行数据库状态刷新..."
    
    # 创建临时SQL文件
    cat > /tmp/refresh_state.sql << EOF
-- 数据库状态刷新查询
SELECT '=== 系统状态刷新开始 ===' as status;

-- 检查菜单配置状态
SELECT COUNT(*) as menu_count FROM jsh_function WHERE delete_flag = '0';

-- 检查权限配置状态  
SELECT COUNT(*) as permission_count FROM jsh_user_business WHERE type = 'RoleFunctions' AND delete_flag = '0';

-- 检查用户状态
SELECT COUNT(*) as user_count FROM jsh_user WHERE status = '0';

SELECT '=== 系统状态刷新完成 ===' as status;
EOF

    # 执行SQL查询（通过Docker容器）
    if docker-compose exec -T jsherp-mysql mysql -u jsh_user -p123456 jsherp-mysql-dev < /tmp/refresh_state.sql; then
        log_success "数据库状态刷新完成"
    else
        log_error "数据库状态刷新失败"
        return 1
    fi
    
    # 清理临时文件
    rm -f /tmp/refresh_state.sql
}

# 清除前端缓存
clear_frontend_cache() {
    log_info "清除前端缓存..."
    
    log_warning "请手动执行以下步骤："
    echo "1. 打开浏览器开发者工具 (F12)"
    echo "2. 在Console中执行: localStorage.clear(); sessionStorage.clear();"
    echo "3. 刷新页面 (Ctrl+F5 或 Cmd+Shift+R)"
    echo "4. 重新登录系统"
    
    read -p "完成上述步骤后按Enter继续..."
    log_success "前端缓存清除完成"
}

# 重启相关服务
restart_services() {
    log_info "重启相关服务..."
    
    read -p "是否需要重启前端服务? (y/N): " restart_frontend
    if [[ $restart_frontend =~ ^[Yy]$ ]]; then
        log_info "重启前端容器..."
        docker-compose restart jsherp-web
        log_success "前端容器重启完成"
    fi
    
    read -p "是否需要重启后端服务? (y/N): " restart_backend
    if [[ $restart_backend =~ ^[Yy]$ ]]; then
        log_info "重启后端容器..."
        docker-compose restart jsherp-boot
        log_success "后端容器重启完成"
    fi
}

# 验证修复结果
verify_fix() {
    log_info "验证修复结果..."
    
    echo "请验证以下项目："
    echo "1. 登录系统 (http://localhost:8080)"
    echo "2. 检查菜单是否显示"
    echo "3. 尝试访问问题页面"
    echo "4. 验证基础功能是否正常"
    
    read -p "修复是否成功? (y/N): " fix_success
    if [[ $fix_success =~ ^[Yy]$ ]]; then
        log_success "问题修复成功！"
        return 0
    else
        log_warning "问题仍未解决，请查看详细诊断指南"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "jshERP模块集成问题快速修复脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -q, --quick    快速修复模式（仅执行数据库刷新）"
    echo "  -f, --full     完整修复模式（执行所有修复步骤）"
    echo ""
    echo "示例:"
    echo "  $0 -q          # 快速修复"
    echo "  $0 -f          # 完整修复"
    echo "  $0             # 交互式修复"
}

# 快速修复模式
quick_fix() {
    log_info "执行快速修复..."
    check_docker_environment
    refresh_database_state
    log_success "快速修复完成，请重新登录系统验证"
}

# 完整修复模式
full_fix() {
    log_info "执行完整修复..."
    check_docker_environment
    refresh_database_state
    clear_frontend_cache
    restart_services
    verify_fix
}

# 交互式修复模式
interactive_fix() {
    echo "========================================"
    echo "jshERP模块集成问题修复工具"
    echo "========================================"
    echo ""
    echo "请选择修复模式:"
    echo "1. 快速修复 (推荐)"
    echo "2. 完整修复"
    echo "3. 仅数据库刷新"
    echo "4. 仅清除缓存"
    echo "5. 仅重启服务"
    echo ""
    
    read -p "请输入选择 (1-5): " choice
    
    case $choice in
        1)
            quick_fix
            ;;
        2)
            full_fix
            ;;
        3)
            check_docker_environment
            refresh_database_state
            ;;
        4)
            clear_frontend_cache
            ;;
        5)
            check_docker_environment
            restart_services
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -q|--quick)
            quick_fix
            ;;
        -f|--full)
            full_fix
            ;;
        "")
            interactive_fix
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
