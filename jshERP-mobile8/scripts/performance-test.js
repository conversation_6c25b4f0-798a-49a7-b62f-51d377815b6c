#!/usr/bin/env node

/**
 * jshERP Mobile 性能测试脚本
 * 
 * 用于自动化性能测试和分析
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class PerformanceTestRunner {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      bundleSize: {},
      buildTime: 0,
      testResults: []
    }
    
    this.distPath = path.resolve(__dirname, '../dist')
    this.reportPath = path.resolve(__dirname, '../performance-reports')
    
    // 确保报告目录存在
    if (!fs.existsSync(this.reportPath)) {
      fs.mkdirSync(this.reportPath, { recursive: true })
    }
  }
  
  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString()
    const prefix = {
      info: '📊',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    }[type] || '📊'
    
    console.log(`${prefix} [${timestamp}] ${message}`)
  }
  
  /**
   * 分析Bundle大小
   */
  analyzeBundleSize() {
    this.log('开始分析Bundle大小...')
    
    if (!fs.existsSync(this.distPath)) {
      this.log('dist目录不存在，请先执行构建', 'error')
      return
    }
    
    const bundleSize = {
      js: 0,
      css: 0,
      images: 0,
      fonts: 0,
      total: 0
    }
    
    const analyzeDirectory = (dir, prefix = '') => {
      const files = fs.readdirSync(dir)
      
      files.forEach(file => {
        const filePath = path.join(dir, file)
        const stat = fs.statSync(filePath)
        
        if (stat.isDirectory()) {
          analyzeDirectory(filePath, prefix + file + '/')
        } else {
          const size = stat.size
          bundleSize.total += size
          
          const ext = path.extname(file).toLowerCase()
          if (ext === '.js') {
            bundleSize.js += size
          } else if (ext === '.css') {
            bundleSize.css += size
          } else if (['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg'].includes(ext)) {
            bundleSize.images += size
          } else if (['.woff', '.woff2', '.ttf', '.eot'].includes(ext)) {
            bundleSize.fonts += size
          }
          
          // 记录大文件
          if (size > 500 * 1024) { // 大于500KB
            this.log(`大文件警告: ${prefix}${file} (${this.formatBytes(size)})`, 'warning')
          }
        }
      })
    }
    
    analyzeDirectory(this.distPath)
    
    this.results.bundleSize = bundleSize
    
    this.log(`Bundle分析完成:`)
    this.log(`  JavaScript: ${this.formatBytes(bundleSize.js)}`)
    this.log(`  CSS: ${this.formatBytes(bundleSize.css)}`)
    this.log(`  图片: ${this.formatBytes(bundleSize.images)}`)
    this.log(`  字体: ${this.formatBytes(bundleSize.fonts)}`)
    this.log(`  总大小: ${this.formatBytes(bundleSize.total)}`)
    
    // 性能建议
    this.provideBundleSizeRecommendations(bundleSize)
  }
  
  /**
   * 提供Bundle大小优化建议
   */
  provideBundleSizeRecommendations(bundleSize) {
    const recommendations = []
    
    if (bundleSize.js > 1024 * 1024) { // 大于1MB
      recommendations.push('JavaScript文件过大，建议进行代码分割')
    }
    
    if (bundleSize.css > 200 * 1024) { // 大于200KB
      recommendations.push('CSS文件较大，建议移除未使用的样式')
    }
    
    if (bundleSize.images > 2 * 1024 * 1024) { // 大于2MB
      recommendations.push('图片资源过大，建议压缩或使用WebP格式')
    }
    
    if (bundleSize.total > 5 * 1024 * 1024) { // 大于5MB
      recommendations.push('总Bundle大小过大，建议启用懒加载')
    }
    
    if (recommendations.length > 0) {
      this.log('优化建议:', 'warning')
      recommendations.forEach(rec => this.log(`  • ${rec}`, 'warning'))
    } else {
      this.log('Bundle大小在合理范围内', 'success')
    }
  }
  
  /**
   * 测试构建时间
   */
  testBuildTime() {
    this.log('开始测试构建时间...')
    
    const startTime = Date.now()
    
    try {
      // 清理dist目录
      if (fs.existsSync(this.distPath)) {
        execSync(`rm -rf ${this.distPath}`, { stdio: 'pipe' })
      }
      
      // 执行构建
      execSync('npm run build-only', { 
        stdio: 'pipe',
        cwd: path.resolve(__dirname, '..')
      })
      
      const buildTime = Date.now() - startTime
      this.results.buildTime = buildTime
      
      this.log(`构建完成，耗时: ${this.formatTime(buildTime)}`)
      
      // 构建时间建议
      if (buildTime > 60000) { // 大于1分钟
        this.log('构建时间较长，建议优化构建配置', 'warning')
      } else if (buildTime < 10000) { // 小于10秒
        this.log('构建速度优秀', 'success')
      } else {
        this.log('构建速度良好', 'success')
      }
      
    } catch (error) {
      this.log(`构建失败: ${error.message}`, 'error')
      throw error
    }
  }
  
  /**
   * 分析依赖项
   */
  analyzeDependencies() {
    this.log('开始分析依赖项...')
    
    const packageJsonPath = path.resolve(__dirname, '../package.json')
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    
    const dependencies = packageJson.dependencies || {}
    const devDependencies = packageJson.devDependencies || {}
    
    const depCount = Object.keys(dependencies).length
    const devDepCount = Object.keys(devDependencies).length
    
    this.log(`生产依赖: ${depCount} 个`)
    this.log(`开发依赖: ${devDepCount} 个`)
    
    // 检查大型依赖
    const largeDependencies = [
      'lodash', 'moment', 'antd', 'element-ui', 'bootstrap'
    ]
    
    const foundLargeDeps = Object.keys(dependencies).filter(dep => 
      largeDependencies.some(large => dep.includes(large))
    )
    
    if (foundLargeDeps.length > 0) {
      this.log('发现大型依赖:', 'warning')
      foundLargeDeps.forEach(dep => this.log(`  • ${dep}`, 'warning'))
    }
    
    // 检查重复依赖
    this.checkDuplicateDependencies(dependencies)
  }
  
  /**
   * 检查重复依赖
   */
  checkDuplicateDependencies(dependencies) {
    const duplicates = []
    const depNames = Object.keys(dependencies)
    
    // 简单的重复检查（基于名称相似性）
    for (let i = 0; i < depNames.length; i++) {
      for (let j = i + 1; j < depNames.length; j++) {
        const dep1 = depNames[i]
        const dep2 = depNames[j]
        
        // 检查是否有相似的包名
        if (dep1.includes(dep2) || dep2.includes(dep1)) {
          duplicates.push([dep1, dep2])
        }
      }
    }
    
    if (duplicates.length > 0) {
      this.log('可能的重复依赖:', 'warning')
      duplicates.forEach(([dep1, dep2]) => {
        this.log(`  • ${dep1} 和 ${dep2}`, 'warning')
      })
    }
  }
  
  /**
   * 生成性能报告
   */
  generateReport() {
    this.log('生成性能报告...')
    
    const report = {
      ...this.results,
      summary: this.generateSummary(),
      recommendations: this.generateRecommendations()
    }
    
    const reportFile = path.join(
      this.reportPath, 
      `performance-report-${Date.now()}.json`
    )
    
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2))
    
    // 生成HTML报告
    this.generateHTMLReport(report)
    
    this.log(`报告已生成: ${reportFile}`, 'success')
  }
  
  /**
   * 生成摘要
   */
  generateSummary() {
    const { bundleSize, buildTime } = this.results
    
    let score = 100
    
    // Bundle大小评分
    if (bundleSize.total > 5 * 1024 * 1024) score -= 20
    else if (bundleSize.total > 3 * 1024 * 1024) score -= 10
    else if (bundleSize.total > 1 * 1024 * 1024) score -= 5
    
    // 构建时间评分
    if (buildTime > 60000) score -= 15
    else if (buildTime > 30000) score -= 8
    else if (buildTime > 15000) score -= 3
    
    return {
      score: Math.max(0, score),
      grade: score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : 'D',
      bundleSizeFormatted: this.formatBytes(bundleSize.total),
      buildTimeFormatted: this.formatTime(buildTime)
    }
  }
  
  /**
   * 生成优化建议
   */
  generateRecommendations() {
    const recommendations = []
    const { bundleSize, buildTime } = this.results
    
    if (bundleSize.js > 1024 * 1024) {
      recommendations.push({
        type: 'bundle',
        priority: 'high',
        message: '启用代码分割和懒加载',
        impact: '可减少初始加载时间50%以上'
      })
    }
    
    if (bundleSize.images > 1024 * 1024) {
      recommendations.push({
        type: 'images',
        priority: 'medium',
        message: '优化图片资源',
        impact: '可减少20-40%的图片大小'
      })
    }
    
    if (buildTime > 30000) {
      recommendations.push({
        type: 'build',
        priority: 'medium',
        message: '优化构建配置',
        impact: '可提升开发体验'
      })
    }
    
    return recommendations
  }
  
  /**
   * 生成HTML报告
   */
  generateHTMLReport(report) {
    const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>jshERP Mobile 性能报告</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
    .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 12px; padding: 24px; box-shadow: 0 2px 12px rgba(0,0,0,0.1); }
    h1 { color: #333; text-align: center; margin-bottom: 32px; }
    .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 32px; }
    .metric { background: #f8f9fa; padding: 16px; border-radius: 8px; text-align: center; }
    .metric-value { font-size: 24px; font-weight: bold; color: #007acc; }
    .metric-label { color: #666; margin-top: 8px; }
    .section { margin-bottom: 32px; }
    .section h2 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 8px; }
    .recommendations { display: grid; gap: 12px; }
    .recommendation { padding: 16px; border-left: 4px solid #007acc; background: #f8f9fa; border-radius: 4px; }
    .priority-high { border-left-color: #f5222d; }
    .priority-medium { border-left-color: #fa8c16; }
    .priority-low { border-left-color: #52c41a; }
  </style>
</head>
<body>
  <div class="container">
    <h1>🚀 jshERP Mobile 性能报告</h1>
    
    <div class="summary">
      <div class="metric">
        <div class="metric-value">${report.summary.grade}</div>
        <div class="metric-label">综合评分</div>
      </div>
      <div class="metric">
        <div class="metric-value">${report.summary.bundleSizeFormatted}</div>
        <div class="metric-label">Bundle大小</div>
      </div>
      <div class="metric">
        <div class="metric-value">${report.summary.buildTimeFormatted}</div>
        <div class="metric-label">构建时间</div>
      </div>
      <div class="metric">
        <div class="metric-value">${new Date(report.timestamp).toLocaleDateString()}</div>
        <div class="metric-label">测试日期</div>
      </div>
    </div>
    
    <div class="section">
      <h2>📊 Bundle 分析</h2>
      <p>JavaScript: ${this.formatBytes(report.bundleSize.js)}</p>
      <p>CSS: ${this.formatBytes(report.bundleSize.css)}</p>
      <p>图片: ${this.formatBytes(report.bundleSize.images)}</p>
      <p>字体: ${this.formatBytes(report.bundleSize.fonts)}</p>
    </div>
    
    <div class="section">
      <h2>💡 优化建议</h2>
      <div class="recommendations">
        ${report.recommendations.map(rec => `
          <div class="recommendation priority-${rec.priority}">
            <strong>${rec.message}</strong>
            <p>${rec.impact}</p>
          </div>
        `).join('')}
      </div>
    </div>
  </div>
</body>
</html>`
    
    const htmlFile = path.join(this.reportPath, `performance-report-${Date.now()}.html`)
    fs.writeFileSync(htmlFile, htmlContent)
    
    this.log(`HTML报告已生成: ${htmlFile}`, 'success')
  }
  
  /**
   * 运行完整测试
   */
  async run() {
    try {
      this.log('开始性能测试...', 'info')
      
      // 1. 测试构建时间
      this.testBuildTime()
      
      // 2. 分析Bundle大小
      this.analyzeBundleSize()
      
      // 3. 分析依赖项
      this.analyzeDependencies()
      
      // 4. 生成报告
      this.generateReport()
      
      this.log('性能测试完成！', 'success')
      
    } catch (error) {
      this.log(`测试失败: ${error.message}`, 'error')
      process.exit(1)
    }
  }
  
  /**
   * 格式化字节数
   */
  formatBytes(bytes) {
    const sizes = ['B', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }
  
  /**
   * 格式化时间
   */
  formatTime(ms) {
    if (ms < 1000) return `${ms}ms`
    return `${(ms / 1000).toFixed(2)}s`
  }
}

// 运行测试
if (require.main === module) {
  const runner = new PerformanceTestRunner()
  runner.run()
}

module.exports = PerformanceTestRunner
