<template>
  <div id="app">
    <router-view v-slot="{ Component, route }">
      <transition
        :name="getTransitionName(route)"
        mode="out-in"
        appear
      >
        <component :is="Component" :key="route.path" />
      </transition>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { RouteLocationNormalized } from 'vue-router'

// 根据路由路径确定过渡动画名称
const getTransitionName = (route: RouteLocationNormalized): string => {
  const path = route.path

  // 订单相关页面使用滑动动画
  if (path.includes('/orders/')) {
    if (path.includes('/add')) {
      return 'slide-left'
    } else if (path.includes('/edit')) {
      return 'slide-left'
    } else if (path.includes('/detail')) {
      return 'slide-left'
    }
    return 'slide-right'
  }

  // 业务模块页面使用淡入淡出
  if (path.includes('/business/') || path.includes('/reports/') || path.includes('/data/')) {
    return 'fade'
  }

  // 认证页面使用缩放动画
  if (path.includes('/auth/') || path.includes('/login') || path.includes('/register')) {
    return 'scale'
  }

  // 默认使用滑动动画
  return 'slide-right'
}
</script>

<style scoped>
#app {
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333333;
  background-color: #F7F8FA;
  min-height: 100vh;
  overflow-x: hidden; /* 防止水平滚动 */
}

/* 页面过渡动画 */

/* 滑动动画 - 向左 */
.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-left-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-100%);
}

/* 滑动动画 - 向右 */
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-100%);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 缩放动画 */
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-enter-from {
  opacity: 0;
  transform: scale(0.9);
}

.scale-leave-to {
  opacity: 0;
  transform: scale(1.1);
}

/* 确保过渡期间的定位 */
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* 为过渡动画提供容器 */
.router-view-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
}
</style>