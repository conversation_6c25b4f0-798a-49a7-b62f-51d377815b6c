<!--
  ERP主布局组件
  
  包含底部Tab导航的主要应用布局
-->
<template>
  <div class="main-layout">
    <!-- 内容区域 -->
    <div class="main-layout__content">
      <router-view v-slot="{ Component, route }">
        <transition
          :name="getTransitionName(route)"
          mode="out-in"
          appear
          @before-enter="onBeforeEnter"
          @after-enter="onAfterEnter"
          @before-leave="onBeforeLeave"
          @after-leave="onAfterLeave"
        >
          <component :is="Component" :key="route.path" />
        </transition>
      </router-view>
    </div>
    
    <!-- 底部Tab导航 -->
    <ERPTabBar />
  </div>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue'
import { useRoute } from 'vue-router'
import type { RouteLocationNormalized } from 'vue-router'
import ERPTabBar from '@/components/erp/ERPTabBar.vue'

const route = useRoute()
const isTransitioning = ref(false)

/**
 * 根据路由路径确定过渡动画名称
 */
const getTransitionName = (route: RouteLocationNormalized): string => {
  const path = route.path

  // 订单相关页面使用滑动动画
  if (path.includes('/orders/')) {
    if (path.includes('/add') || path.includes('/edit') || path.includes('/detail')) {
      return 'slide-left'
    }
    return 'slide-right'
  }

  // 业务模块页面使用淡入淡出
  if (path.includes('/business/') || path.includes('/reports/') || path.includes('/data/')) {
    return 'fade'
  }

  // 默认使用淡入淡出
  return 'fade'
}

/**
 * 过渡动画生命周期钩子
 */
const onBeforeEnter = (el: Element) => {
  isTransitioning.value = true
  // 添加过渡期间的性能优化类
  el.classList.add('page-transition-active')
}

const onAfterEnter = (el: Element) => {
  isTransitioning.value = false
  // 移除过渡期间的性能优化类
  el.classList.remove('page-transition-active')
}

const onBeforeLeave = (el: Element) => {
  isTransitioning.value = true
  el.classList.add('page-transition-active')
}

const onAfterLeave = (el: Element) => {
  isTransitioning.value = false
  el.classList.remove('page-transition-active')
}

/**
 * 监听路由变化，更新页面标题
 */
watch(
  () => route.meta.title,
  (title) => {
    if (title) {
      document.title = `${title} - jshERP移动端`
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.main-layout {
  min-height: 100vh;
  background: var(--erp-bg-secondary);
  
  &__content {
    min-height: 100vh;
    padding-bottom: 60px; // Tab栏高度
    overflow-x: hidden;
    position: relative;

    // 过渡期间的容器样式
    &.transitioning {
      pointer-events: none;
    }
  }
}

// 导入过渡动画样式
@import '@/styles/transitions.less';

// 布局特定的过渡动画优化
.main-layout__content {
  // 确保过渡动画的容器定位
  .slide-left-enter-active,
  .slide-left-leave-active,
  .slide-right-enter-active,
  .slide-right-leave-active {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: calc(100vh - 60px); // 减去Tab栏高度
  }

  // 淡入淡出动画不需要绝对定位
  .fade-enter-active,
  .fade-leave-active {
    position: relative;
  }
}

// 响应式优化
@media (max-width: @erp-mobile-xs) {
  .main-layout {
    &__content {
      padding-bottom: 50px; // 小屏幕调整Tab栏高度

      .slide-left-enter-active,
      .slide-left-leave-active,
      .slide-right-enter-active,
      .slide-right-leave-active {
        height: calc(100vh - 50px);
      }
    }
  }
}
</style>
