/**
 * jshERP Mobile 通用业务类型定义
 * 
 * 定义所有业务模块共享的数据类型和接口
 */

// 基础实体接口
export interface BaseEntity {
  id?: string | number
  createTime?: string
  updateTime?: string
  tenantId?: string | number
  deleteFlag?: string
}

// 往来单位基础接口（客户、供应商等）
export interface BasePartner extends BaseEntity {
  name: string
  code?: string
  contact?: string
  phoneNumber?: string
  email?: string
  address?: string
  remark?: string
  enabled?: boolean
}

// 客户信息
export interface Customer extends BasePartner {
  type: 'customer'
  customerLevel?: string
  creditLimit?: number
  paymentTerms?: string
}

// 供应商信息
export interface Supplier extends BasePartner {
  type: 'supplier'
  supplierLevel?: string
  paymentMethod?: string
  deliveryTerms?: string
}

// 商品基础信息
export interface Product extends BaseEntity {
  name: string
  code: string
  barCode?: string
  categoryId?: string | number
  categoryName?: string
  unitId?: string | number
  unitName?: string
  specification?: string
  model?: string
  color?: string
  standard?: string
  mfrs?: string
  otherField1?: string
  otherField2?: string
  otherField3?: string
  enableSerialNumber?: boolean
  enableBatchNumber?: boolean
  remark?: string
  imgName?: string
  imgUrl?: string
  enabled?: boolean
  // 价格相关
  purchasePrice?: number
  wholesalePrice?: number
  lowPrice?: number
  // 库存相关
  currentStock?: number
  safetyStock?: number
}

// 仓库信息
export interface Depot extends BaseEntity {
  name: string
  code?: string
  address?: string
  warehousing?: number
  truckage?: number
  type?: number
  sort?: string
  remark?: string
  enabled?: boolean
  isDefault?: boolean
}

// 账户信息
export interface Account extends BaseEntity {
  name: string
  serialNo?: string
  initialAmount?: number
  currentAmount?: number
  remark?: string
  enabled?: boolean
  isDefault?: boolean
}

// 单据明细项基础接口
export interface BaseDetailItem extends BaseEntity {
  headerId?: string | number
  productId: string | number
  productName: string
  productCode?: string
  productModel?: string
  productUnit?: string
  productBarCode?: string
  depotId?: string | number
  depotName?: string
  basicNumber?: number
  unitPrice?: number
  taxRate?: number
  taxMoney?: number
  taxLastMoney?: number
  allPrice?: number
  mType?: string
  remark?: string
  imgName?: string
  imgUrl?: string
  // 序列号和批次号
  snList?: string
  batchNumber?: string
  expirationDate?: string
  // 扩展字段
  otherField1?: string
  otherField2?: string
  otherField3?: string
  otherField4?: string
  otherField5?: string
}

// 单据头基础接口
export interface BaseDocumentHeader extends BaseEntity {
  type: string
  subType: string
  number: string
  operTime: string
  partnerId?: string | number
  partnerName?: string
  accountId?: string | number
  accountName?: string
  totalPrice?: number
  payType?: string
  remark?: string
  fileName?: string
  status?: string
  // 审核相关
  reviewStatus?: string
  reviewer?: string
  reviewTime?: string
  // 关联单据
  linkNumber?: string
  linkId?: string | number
  // 扩展字段
  otherField1?: string
  otherField2?: string
  otherField3?: string
}

// 业务单据类型枚举
export enum DocumentType {
  // 销售相关
  SALES_ORDER = 'sales_order',
  SALES_OUT = 'sales_out',
  SALES_RETURN = 'sales_return',
  RETAIL_OUT = 'retail_out',
  RETAIL_RETURN = 'retail_return',
  
  // 采购相关
  PURCHASE_ORDER = 'purchase_order',
  PURCHASE_IN = 'purchase_in',
  PURCHASE_RETURN = 'purchase_return',
  
  // 库存相关
  OTHER_IN = 'other_in',
  OTHER_OUT = 'other_out',
  TRANSFER_OUT = 'transfer_out',
  ASSEMBLY = 'assembly',
  DISASSEMBLY = 'disassembly',
  INVENTORY_CHECK = 'inventory_check',
  
  // 财务相关
  RECEIPT = 'receipt',
  PAYMENT = 'payment',
  TRANSFER = 'transfer',
  INCOME = 'income',
  EXPENSE = 'expense'
}

// 单据状态枚举
export enum DocumentStatus {
  DRAFT = '0',           // 草稿
  PENDING = '1',         // 待审核
  APPROVED = '2',        // 已审核
  REJECTED = '3',        // 已拒绝
  CANCELLED = '4',       // 已取消
  COMPLETED = '5'        // 已完成
}

// 审核状态枚举
export enum ReviewStatus {
  PENDING = '0',         // 待审核
  APPROVED = '1',        // 已通过
  REJECTED = '2'         // 已拒绝
}

// 表单配置接口
export interface FormConfig {
  // 表单类型
  formType: DocumentType
  // 是否显示往来单位选择
  showPartnerSelector?: boolean
  // 往来单位类型
  partnerType?: 'customer' | 'supplier'
  // 是否显示仓库选择
  showDepotSelector?: boolean
  // 是否显示账户选择
  showAccountSelector?: boolean
  // 是否显示关联单据
  showLinkDocument?: boolean
  // 是否可编辑
  editable?: boolean
  // 默认值
  defaultValues?: Partial<BaseDocumentHeader>
  // 字段显示配置
  fieldConfig?: {
    [key: string]: {
      show?: boolean
      required?: boolean
      readonly?: boolean
      label?: string
      placeholder?: string
    }
  }
}

// API响应基础接口
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
  timestamp: string
}

// 分页查询参数
export interface PageQuery {
  current: number
  size: number
  total?: number
}

// 分页响应数据
export interface PageResult<T = any> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 查询条件基础接口
export interface BaseQuery extends PageQuery {
  keyword?: string
  startDate?: string
  endDate?: string
  status?: string
  type?: string
  partnerId?: string | number
  depotId?: string | number
}

// 操作结果接口
export interface OperationResult {
  success: boolean
  message: string
  data?: any
}

// 文件上传接口
export interface FileUpload {
  name: string
  url: string
  size: number
  type: string
  uid: string
}

// 系统配置接口
export interface SystemConfig {
  companyName?: string
  companyLogo?: string
  systemName?: string
  defaultDepotId?: string | number
  defaultAccountId?: string | number
  enableSerialNumber?: boolean
  enableBatchNumber?: boolean
  enableMultiLevel?: boolean
  decimalDigits?: number
}

// 用户权限接口
export interface UserPermission {
  userId: string | number
  userName: string
  roleIds: (string | number)[]
  permissions: string[]
  depotIds: (string | number)[]
  customerIds: (string | number)[]
  supplierIds: (string | number)[]
}

// 导出接口
export interface ExportConfig {
  fileName: string
  fileType: 'excel' | 'pdf' | 'csv'
  columns: string[]
  data: any[]
}

// 打印配置接口
export interface PrintConfig {
  templateId: string
  templateName: string
  paperSize: 'A4' | 'A5' | 'A3'
  orientation: 'portrait' | 'landscape'
  copies: number
}

// 工作流配置接口
export interface WorkflowConfig {
  workflowId: string
  workflowName: string
  currentStep: number
  totalSteps: number
  approvers: string[]
  status: 'pending' | 'approved' | 'rejected'
}
