/**
 * 采购管理相关类型定义
 * 
 * 基于通用业务类型，定义采购管理特定的数据结构
 */

import type { 
  BaseDocumentHeader, 
  BaseDetailItem, 
  Supplier,
  Product as BaseProduct,
  FileUpload
} from './business'

/**
 * 采购商品信息
 */
export interface PurchaseProduct extends BaseProduct {
  /** 商品规格 */
  spec?: string
  /** 采购数量 */
  quantity: number
  /** 采购单价 */
  purchasePrice: number
  /** 供应商商品编码 */
  supplierProductCode?: string
  /** 最小采购量 */
  minOrderQuantity?: number
  /** 交货期（天） */
  deliveryDays?: number
}

/**
 * 采购员信息
 */
export interface Purchaser {
  id: string | number
  name: string
  code?: string
  department?: string
  phoneNumber?: string
  email?: string
  enabled?: boolean
}

/**
 * 采购订单表单数据
 */
export interface PurchaseOrderForm extends BaseDocumentHeader {
  // 基础信息
  orderNo: string
  supplierName: string
  supplierId: string | number
  orderDate: string
  purchaser: string
  purchaserId: string | number
  
  // 商品信息
  products: PurchaseProduct[]
  
  // 金额信息
  subtotal: number
  discountRate: number
  discountAmount: number
  finalAmount: number
  taxRate?: number
  taxAmount?: number
  
  // 交货信息
  expectedDeliveryDate?: string
  deliveryAddress?: string
  contactPerson?: string
  contactPhone?: string
  
  // 结算信息
  paymentAccount?: string
  paymentTerms?: string
  prepaidAmount?: number
  
  // 附加信息
  remark?: string
  attachments?: FileUpload[]
}

/**
 * 采购入库表单数据
 */
export interface PurchaseInForm extends BaseDocumentHeader {
  // 基础信息
  inNo: string
  supplierName: string
  supplierId: string | number
  inDate: string
  purchaser: string
  purchaserId: string | number
  
  // 关联信息
  relatedOrderNo?: string
  relatedOrderId?: string | number
  
  // 商品信息
  products: PurchaseProduct[]
  
  // 金额信息
  subtotal: number
  discountRate: number
  discountAmount: number
  finalAmount: number
  
  // 仓库信息
  warehouseId: string | number
  warehouseName: string
  
  // 质检信息
  qualityCheckStatus?: 'pending' | 'passed' | 'failed'
  qualityCheckRemark?: string
  
  // 附加信息
  remark?: string
  attachments?: FileUpload[]
}

/**
 * 采购退货表单数据
 */
export interface PurchaseReturnForm extends BaseDocumentHeader {
  // 基础信息
  returnNo: string
  supplierName: string
  supplierId: string | number
  returnDate: string
  purchaser: string
  purchaserId: string | number
  
  // 关联信息
  relatedInNo?: string
  relatedInId?: string | number
  
  // 商品信息
  products: PurchaseProduct[]
  
  // 金额信息
  subtotal: number
  discountRate: number
  discountAmount: number
  finalAmount: number
  
  // 退货信息
  returnReason: string
  returnType: 'quality' | 'quantity' | 'other'
  
  // 仓库信息
  warehouseId: string | number
  warehouseName: string
  
  // 附加信息
  remark?: string
  attachments?: FileUpload[]
}

/**
 * 采购明细项
 */
export interface PurchaseDetailItem extends BaseDetailItem {
  // 采购特定字段
  supplierPrice?: number
  lastPurchasePrice?: number
  averagePurchasePrice?: number
  qualityGrade?: string
  qualityCheckResult?: 'passed' | 'failed' | 'pending'
  qualityCheckRemark?: string
  
  // 交货信息
  expectedDeliveryDate?: string
  actualDeliveryDate?: string
  deliveryStatus?: 'pending' | 'partial' | 'completed' | 'delayed'
  
  // 退货信息
  returnableQuantity?: number
  returnedQuantity?: number
  returnReason?: string
}

/**
 * 采购订单状态
 */
export enum PurchaseOrderStatus {
  DRAFT = 'draft',           // 草稿
  PENDING = 'pending',       // 待审核
  APPROVED = 'approved',     // 已审核
  ORDERED = 'ordered',       // 已下单
  PARTIAL_IN = 'partial_in', // 部分入库
  COMPLETED = 'completed',   // 已完成
  CANCELLED = 'cancelled'    // 已取消
}

/**
 * 采购入库状态
 */
export enum PurchaseInStatus {
  DRAFT = 'draft',           // 草稿
  PENDING = 'pending',       // 待审核
  APPROVED = 'approved',     // 已审核
  IN_STORAGE = 'in_storage', // 已入库
  QUALITY_CHECK = 'quality_check', // 质检中
  COMPLETED = 'completed',   // 已完成
  CANCELLED = 'cancelled'    // 已取消
}

/**
 * 采购退货状态
 */
export enum PurchaseReturnStatus {
  DRAFT = 'draft',           // 草稿
  PENDING = 'pending',       // 待审核
  APPROVED = 'approved',     // 已审核
  RETURNED = 'returned',     // 已退货
  REFUNDED = 'refunded',     // 已退款
  COMPLETED = 'completed',   // 已完成
  CANCELLED = 'cancelled'    // 已取消
}

/**
 * 采购操作类型
 */
export type PurchaseAction = 
  | 'create'
  | 'edit'
  | 'view'
  | 'copy'
  | 'delete'
  | 'submit'
  | 'approve'
  | 'reject'
  | 'cancel'
  | 'order'
  | 'receive'
  | 'return'
  | 'quality_check'

/**
 * 采购查询参数
 */
export interface PurchaseQuery {
  current: number
  size: number
  keyword?: string
  supplierName?: string
  purchaser?: string
  orderStatus?: string
  startDate?: string
  endDate?: string
  minAmount?: number
  maxAmount?: number
}

/**
 * 采购统计信息
 */
export interface PurchaseStatistics {
  totalOrders: number
  totalAmount: number
  paidAmount: number
  unpaidAmount: number
  todayOrders: number
  todayAmount: number
  monthOrders: number
  monthAmount: number
  averageOrderValue: number
  topSuppliers: { name: string; amount: number }[]
  topProducts: { name: string; quantity: number }[]
}

/**
 * 供应商采购历史
 */
export interface SupplierPurchaseHistory {
  supplierId: string | number
  supplierName: string
  totalOrders: number
  totalAmount: number
  lastOrderDate?: string
  lastOrderAmount?: number
  averageOrderAmount: number
  averageDeliveryDays: number
  qualityScore: number
  orders: PurchaseOrderForm[]
}

/**
 * 采购员业绩
 */
export interface PurchaserPerformance {
  purchaserId: string | number
  purchaserName: string
  totalOrders: number
  totalAmount: number
  costSavings: number
  monthOrders: number
  monthAmount: number
  monthCostSavings: number
  averageOrderValue: number
  supplierCount: number
  rank: number
}

/**
 * 商品采购分析
 */
export interface ProductPurchaseAnalysis {
  productId: string | number
  productName: string
  productCode?: string
  totalQuantity: number
  totalAmount: number
  orderCount: number
  supplierCount: number
  averagePrice: number
  lowestPrice: number
  highestPrice: number
  lastPurchaseDate?: string
  lastPurchasePrice?: number
  trend: 'up' | 'down' | 'stable'
  seasonality?: 'high' | 'medium' | 'low'
}

/**
 * 采购预算管理
 */
export interface PurchaseBudget {
  budgetId: string
  budgetName: string
  budgetYear: number
  budgetMonth?: number
  department?: string
  category?: string
  budgetAmount: number
  usedAmount: number
  remainingAmount: number
  usageRate: number
  status: 'active' | 'exceeded' | 'completed' | 'cancelled'
  approver: string
  createdBy: string
  createdTime: string
}

/**
 * 采购合同管理
 */
export interface PurchaseContract {
  contractId: string
  contractNo: string
  contractName: string
  supplierId: string | number
  supplierName: string
  contractType: 'framework' | 'specific' | 'service'
  contractAmount: number
  startDate: string
  endDate: string
  paymentTerms: string
  deliveryTerms: string
  qualityStandards: string
  penaltyClause?: string
  status: 'draft' | 'active' | 'expired' | 'terminated'
  attachments?: FileUpload[]
  createdBy: string
  createdTime: string
}
