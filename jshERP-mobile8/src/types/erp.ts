/**
 * ERP系统通用类型定义
 */

/**
 * 功能项接口
 */
export interface FunctionItem {
  id: string
  icon: string
  label: string
  color?: string
  path?: string
  badge?: number
  disabled?: boolean
  permission?: string
  data?: any
}

/**
 * 功能分组接口
 */
export interface FunctionSection {
  title: string
  items: FunctionItem[]
}

/**
 * 数据卡片配置接口
 */
export interface DataCard {
  key: string
  value: number | string
  label: string
  type?: 'number' | 'currency' | 'percentage'
  showRefresh?: boolean
}

/**
 * 快捷功能接口
 */
export interface QuickAction {
  id: string
  icon: string
  label: string
  color?: string
  path?: string
  badge?: number
}

/**
 * 统计数据接口
 */
export interface StatisticsData {
  yesterday: { sales: number; retail: number; purchase: number }
  monthly: { sales: number; retail: number; purchase: number }
  yearly: { sales: number; retail: number; purchase: number }
  summary: { inventoryTotal: number; accountTotal: number; yearlyProfit: number }
}

/**
 * 用户信息接口
 */
export interface UserInfo {
  username: string
  userType: string
  avatar?: string
}

/**
 * 试用信息接口
 */
export interface TrialInfo {
  isTrialUser: boolean
  endDate: string
  currentUsers: number
  maxUsers: number
}

/**
 * 菜单项接口
 */
export interface MenuItem {
  id: string
  icon: string
  label: string
  description?: string
  danger?: boolean
  path?: string
}

/**
 * 趋势数据接口
 */
export interface TrendData {
  type: 'up' | 'down' | 'flat'
  value: string
}
