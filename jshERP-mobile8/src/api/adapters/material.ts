/**
 * 商品管理API适配器
 * 
 * 提供商品相关的API接口调用功能
 */

import { BaseAdapter } from './base'
import type {
  Material,
  MaterialVo4Unit,
  MaterialQuery,
  MaterialListResponse,
  MaterialDetailResponse,
  MaterialSearchResponse,
  MaterialSearchResult,
  MaterialCategory,
  MaterialStock,
  MaterialPrice,
  ApiResponse
} from '@/types/material'

/**
 * 商品管理API适配器类
 */
export class MaterialAdapter extends BaseAdapter {
  
  /**
   * 获取商品列表
   * @param params 查询参数
   * @returns 商品列表响应
   */
  async getList(params: MaterialQuery): Promise<MaterialListResponse> {
    try {
      // 构建搜索参数字符串
      const searchParams = this.buildSearchParams(params)
      
      const response = await this.request<MaterialVo4Unit[]>({
        url: '/material/list',
        method: 'GET',
        params: {
          search: searchParams,
          current: params.current || 1,
          size: params.size || 20
        }
      })

      // 处理图片URL
      const processedData = this.processImageUrls(response.data.rows || [])

      return {
        records: processedData,
        total: response.data.total || 0,
        size: response.data.size || 20,
        current: response.data.current || 1,
        pages: Math.ceil((response.data.total || 0) / (response.data.size || 20))
      }
    } catch (error) {
      console.error('获取商品列表失败:', error)
      throw error
    }
  }

  /**
   * 根据ID获取商品详情
   * @param id 商品ID
   * @returns 商品详情响应
   */
  async getById(id: number): Promise<MaterialDetailResponse> {
    try {
      const response = await this.request<Material>({
        url: '/material/info',
        method: 'GET',
        params: { id }
      })

      // 处理图片URL
      if (response.data.info && response.data.info.imgName) {
        response.data.info.imgUrl = this.getImageUrl(response.data.info.imgName)
      }

      return {
        success: response.success,
        code: response.code,
        message: response.message,
        data: response.data.info,
        timestamp: response.timestamp
      }
    } catch (error) {
      console.error('获取商品详情失败:', error)
      throw error
    }
  }

  /**
   * 根据关键词搜索商品
   * @param keyword 搜索关键词
   * @returns 搜索结果响应
   */
  async searchByKeyword(keyword: string): Promise<MaterialSearchResponse> {
    try {
      const response = await this.request<MaterialSearchResult[]>({
        url: '/material/getMaterialByParam',
        method: 'GET',
        params: { q: keyword }
      })

      // 处理搜索结果数据格式
      const processedData = this.processSearchResults(response.data || [])

      return {
        success: response.success,
        code: response.code,
        message: response.message,
        data: processedData,
        timestamp: response.timestamp
      }
    } catch (error) {
      console.error('搜索商品失败:', error)
      throw error
    }
  }

  /**
   * 新增商品
   * @param material 商品信息
   * @returns 操作结果
   */
  async add(material: Material): Promise<ApiResponse<number>> {
    try {
      const response = await this.request<number>({
        url: '/material/add',
        method: 'POST',
        data: material
      })

      return response
    } catch (error) {
      console.error('新增商品失败:', error)
      throw error
    }
  }

  /**
   * 更新商品
   * @param material 商品信息
   * @returns 操作结果
   */
  async update(material: Material): Promise<ApiResponse<number>> {
    try {
      const response = await this.request<number>({
        url: '/material/update',
        method: 'PUT',
        data: material
      })

      return response
    } catch (error) {
      console.error('更新商品失败:', error)
      throw error
    }
  }

  /**
   * 删除商品
   * @param ids 商品ID数组
   * @returns 操作结果
   */
  async delete(ids: number[]): Promise<ApiResponse<number>> {
    try {
      const response = await this.request<number>({
        url: '/material/delete',
        method: 'DELETE',
        data: { ids: ids.join(',') }
      })

      return response
    } catch (error) {
      console.error('删除商品失败:', error)
      throw error
    }
  }

  /**
   * 检查商品是否存在
   * @param params 检查参数
   * @returns 检查结果
   */
  async checkExist(params: Partial<Material>): Promise<ApiResponse<boolean>> {
    try {
      const response = await this.request<{ status: boolean }>({
        url: '/material/checkIsExist',
        method: 'GET',
        params
      })

      return {
        success: response.success,
        code: response.code,
        message: response.message,
        data: response.data.status,
        timestamp: response.timestamp
      }
    } catch (error) {
      console.error('检查商品存在性失败:', error)
      throw error
    }
  }

  /**
   * 获取商品分类列表
   * @returns 分类列表
   */
  async getCategories(): Promise<ApiResponse<MaterialCategory[]>> {
    try {
      const response = await this.request<MaterialCategory[]>({
        url: '/materialCategory/list',
        method: 'GET'
      })

      return response
    } catch (error) {
      console.error('获取商品分类失败:', error)
      throw error
    }
  }

  /**
   * 获取商品库存信息
   * @param materialId 商品ID
   * @returns 库存信息
   */
  async getStock(materialId: number): Promise<ApiResponse<MaterialStock[]>> {
    try {
      const response = await this.request<MaterialStock[]>({
        url: '/material/getStock',
        method: 'GET',
        params: { materialId }
      })

      return response
    } catch (error) {
      console.error('获取商品库存失败:', error)
      throw error
    }
  }

  /**
   * 构建搜索参数字符串
   * @param params 查询参数
   * @returns 搜索参数字符串
   */
  private buildSearchParams(params: MaterialQuery): string {
    const searchObj: Record<string, any> = {}
    
    // 添加非空参数
    Object.keys(params).forEach(key => {
      const value = params[key as keyof MaterialQuery]
      if (value !== undefined && value !== null && value !== '') {
        // 排除分页参数
        if (!['current', 'size', 'total'].includes(key)) {
          searchObj[key] = value
        }
      }
    })

    return JSON.stringify(searchObj)
  }

  /**
   * 处理图片URL
   * @param materials 商品列表
   * @returns 处理后的商品列表
   */
  private processImageUrls(materials: MaterialVo4Unit[]): MaterialVo4Unit[] {
    return materials.map(material => ({
      ...material,
      imgUrl: material.imgName ? this.getImageUrl(material.imgName) : undefined
    }))
  }

  /**
   * 处理搜索结果数据
   * @param data 原始搜索结果
   * @returns 处理后的搜索结果
   */
  private processSearchResults(data: any[]): MaterialSearchResult[] {
    return data.map(item => ({
      id: item.id || item.Id,
      name: item.name || item.Name,
      model: item.model || item.Model,
      standard: item.standard || item.Standard,
      barCode: item.barCode || item.BarCode,
      unitName: item.unitName || item.UnitName,
      stock: item.stock || item.Stock || 0,
      imgUrl: item.imgName ? this.getImageUrl(item.imgName) : undefined
    }))
  }

  /**
   * 获取图片URL
   * @param imgName 图片名称
   * @returns 图片URL
   */
  private getImageUrl(imgName: string): string {
    if (!imgName) return ''
    
    // 如果已经是完整URL，直接返回
    if (imgName.startsWith('http')) {
      return imgName
    }
    
    // 构建图片URL
    const baseUrl = this.getBaseURL()
    return `${baseUrl}/file/download/${imgName}`
  }
}

// 导出单例实例
export const materialAdapter = new MaterialAdapter()
