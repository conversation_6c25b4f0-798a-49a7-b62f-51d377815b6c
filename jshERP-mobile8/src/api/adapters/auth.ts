import { BaseAdapter } from './base'
import { httpClient } from '@/utils/request'
import md5 from 'md5'
import type { LoginCredentials, UserInfo } from '@/stores/modules/auth'

export interface LoginResponse {
  token: string
  refreshToken?: string
  userInfo: UserInfo
  permissions: string[]
  expiresIn: number
  tenantId: number
}

export interface CaptchaResponse {
  captchaId: string
  captchaImage: string
}

export class AuthAdapter extends BaseAdapter<LoginCredentials, LoginResponse> {
  protected endpoint = '/user'

  transform(rawData: any): LoginResponse {
    // jshERP登录响应格式适配
    const response = rawData.data || rawData

    // 检查登录状态消息
    if (response.msgTip !== 'user can login') {
      // 处理各种登录失败情况
      switch (response.msgTip) {
        case 'user is not exist':
          throw new Error('用户不存在')
        case 'user password error':
          throw new Error('用户密码不正确')
        case 'user is black':
          throw new Error('用户被禁用')
        case 'tenant is black':
          throw new Error('用户所属的租户被禁用')
        case 'tenant is expire':
          throw new Error('试用期已结束，请联系客服续费')
        default:
          throw new Error('登录失败：' + response.msgTip)
      }
    }

    // 登录成功，解析用户信息
    const userInfo = response.user || {}

    return {
      token: response.token || 'mock-jwt-token',
      userInfo: {
        id: userInfo.id?.toString() || '1',
        username: userInfo.loginName || userInfo.username || 'user',
        realname: userInfo.realname || userInfo.username || 'User',
        avatar: userInfo.avatar || '/default-avatar.png',
        email: userInfo.email,
        phone: userInfo.phonenum || userInfo.phone
      },
      permissions: ['dashboard:view', 'sales:manage', 'purchase:manage', 'inventory:manage'],
      tenantId: userInfo.tenantId || 1
    }
  }
  
  /**
   * 获取验证码
   */
  async getCaptcha(): Promise<CaptchaResponse> {
    try {
      const response = await httpClient.get(`${this.endpoint}/randomImage`)

      // jshERP返回格式：{code: 200, data: {base64: "data:image/svg+xml;base64,...", uuid: "..."}}
      const data = response.data || response

      return {
        captchaId: data.uuid || Date.now().toString(),
        captchaImage: data.base64 || data.data?.base64
      }
    } catch (error) {
      console.error('获取验证码失败:', error)
      throw new Error('获取验证码失败，请重试')
    }
  }

  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials & { captcha: string; captchaId?: string }): Promise<LoginResponse> {
    try {
      const response = await httpClient.post(`${this.endpoint}/login`, {
        loginName: credentials.username, // jshERP使用loginName
        password: md5(credentials.password), // jshERP需要MD5加密密码
        code: credentials.captcha, // jshERP使用code
        uuid: credentials.captchaId // jshERP需要验证码的uuid
      })

      // 正确处理jshERP响应格式
      // response结构：{code: 200, data: {user, msgTip, token}}

      if (response.code !== 200) {
        // 处理验证码错误
        if (response.code === 500010) {
          throw new Error('验证码错误')
        }
        // 处理其他错误
        throw new Error(response.data || response.message || '登录失败')
      }

      // 传递数据给transform：response.data包含{user, msgTip, token}
      return this.transform(response.data)
    } catch (error: any) {
      console.error('登录失败:', error)

      // 处理jshERP特定错误
      if (error.response?.data?.code === 500012) {
        throw new Error('验证码错误或已过期')
      } else if (error.response?.data?.code === 500011) {
        throw new Error('用户名或密码错误')
      } else if (error.message) {
        throw new Error(error.message)
      } else {
        throw new Error('登录失败，请检查网络连接')
      }
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      await httpClient.get(`${this.endpoint}/logout`)
    } catch (error) {
      console.error('登出失败:', error)
      // 即使登出失败也要清除本地状态
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<UserInfo> {
    try {
      const response = await httpClient.get(`${this.endpoint}/getUserSession`)
      const data = response.data || response

      return {
        id: data.id || data.userId,
        username: data.username || data.loginName,
        realname: data.realname || data.name || data.username,
        avatar: data.avatar || data.headImg || '/default-avatar.png',
        email: data.email,
        phone: data.phone || data.phoneNumber
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw new Error('获取用户信息失败')
    }
  }

  /**
   * 获取用户权限
   */
  async getPermissions(): Promise<string[]> {
    try {
      // jshERP可能没有专门的权限API，暂时返回默认权限
      return ['dashboard:view', 'sales:manage', 'purchase:manage', 'inventory:manage']
    } catch (error) {
      console.error('获取权限失败:', error)
      return []
    }
  }
}

export const authAdapter = new AuthAdapter()