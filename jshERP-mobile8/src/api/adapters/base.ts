import { httpClient } from '@/utils/request'

/**
 * API适配器基类
 *
 * 提供统一的API调用接口和数据转换机制
 * 所有具体的API适配器都应该继承此基类
 *
 * @template TRequest 请求参数类型
 * @template TResponse 响应数据类型
 */
export abstract class BaseAdapter<TRequest, TResponse> {
  /** API端点路径 */
  protected abstract endpoint: string

  /**
   * 数据转换方法
   * 将原始API响应数据转换为业务需要的格式
   *
   * @param rawData 原始API响应数据
   * @returns 转换后的业务数据
   */
  abstract transform(rawData: any): TResponse
  
  async request(params: TRequest): Promise<TResponse> {
    try {
      const response = await httpClient.post(this.endpoint, params)
      return this.transform(response)
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }
  
  async get(params?: any): Promise<TResponse> {
    try {
      const response = await httpClient.get(this.endpoint, { params })
      return this.transform(response)
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }
  
  async post(data: TRequest): Promise<TResponse> {
    try {
      const response = await httpClient.post(this.endpoint, data)
      return this.transform(response)
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }
  
  async put(data: TRequest): Promise<TResponse> {
    try {
      const response = await httpClient.put(this.endpoint, data)
      return this.transform(response)
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }
  
  async delete(id: string | number): Promise<void> {
    try {
      await httpClient.delete(`${this.endpoint}/${id}`)
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }
  
  protected handleError(error: any): void {
    console.error(`API Error [${this.endpoint}]:`, error)
    
    // 可以在这里添加错误上报逻辑
    if (error.response) {
      // 服务器响应错误
      console.error('Response error:', error.response.data)
    } else if (error.request) {
      // 网络错误
      console.error('Network error:', error.request)
    } else {
      // 其他错误
      console.error('Error:', error.message)
    }
  }
}