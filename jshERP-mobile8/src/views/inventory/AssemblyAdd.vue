<template>
  <div class="assembly-add">
    <!-- 头部导航 -->
    <van-nav-bar
      title="组装单"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
    />

    <!-- 材料库存警告 -->
    <div v-if="materialStockWarnings.length > 0" class="material-warnings">
      <van-notice-bar
        v-for="warning in materialStockWarnings"
        :key="warning.materialId"
        type="danger"
        :text="warning.message"
        left-icon="warning-o"
        scrollable
      />
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <!-- 基础信息 -->
      <van-cell-group title="基础信息" inset>
        <van-field
          v-model="assemblyForm.assemblyNo"
          label="组装单号"
          placeholder="系统自动生成"
          readonly
          right-icon="refresh"
          @click-right-icon="assemblyForm.assemblyNo = generateAssemblyNo()"
        />
        
        <van-field
          v-model="assemblyForm.assemblyDate"
          label="组装日期"
          placeholder="请选择组装日期"
          readonly
          is-link
          @click="showDatePicker = true"
        />
        
        <van-field
          v-model="assemblyForm.operator"
          label="操作员"
          placeholder="请输入操作员"
          required
        />
        
        <van-field
          v-model="assemblyForm.warehouseName"
          label="仓库"
          placeholder="请选择仓库"
          readonly
          is-link
          required
          @click="showWarehouseSelector = true"
        />
        
        <van-field
          :value="getAssemblyTypeLabel()"
          label="组装类型"
          placeholder="请选择组装类型"
          readonly
          is-link
          required
          @click="showAssemblyTypeSelector = true"
        />
      </van-cell-group>

      <!-- 原材料信息 -->
      <van-cell-group title="原材料" inset>
        <van-cell
          title="添加原材料"
          is-link
          @click="showMaterialSelector = true"
        >
          <template #icon>
            <van-icon name="plus" class="add-icon" />
          </template>
        </van-cell>
        
        <!-- 材料列表 -->
        <div v-if="assemblyForm.materials.length > 0" class="material-list">
          <div
            v-for="(material, index) in assemblyForm.materials"
            :key="material.id"
            class="material-item"
          >
            <div class="material-info">
              <div class="material-name">{{ material.name }}</div>
              <div class="material-details">
                <span class="material-code">{{ material.code }}</span>
                <span class="material-unit">{{ material.unit }}</span>
                <span class="material-price">¥{{ material.unitPrice }}</span>
              </div>
              
              <!-- 库存状态指示器 -->
              <div class="stock-status">
                <van-tag 
                  v-if="isMaterialStockSufficient(index)"
                  type="success" 
                  size="small"
                >
                  库存充足
                </van-tag>
                <van-tag 
                  v-else
                  type="danger" 
                  size="small"
                >
                  库存不足
                </van-tag>
              </div>
            </div>
            
            <div class="material-actions">
              <van-stepper
                v-model="material.quantity"
                min="0"
                step="1"
                @change="updateMaterialQuantity(material.id, $event)"
              />
              <van-button
                type="danger"
                size="mini"
                @click="removeMaterial(material.id)"
              >
                删除
              </van-button>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <van-empty
          v-else
          description="暂无原材料"
          image="search"
        />
      </van-cell-group>

      <!-- 成品信息 -->
      <van-cell-group title="组装成品" inset>
        <van-cell
          title="添加成品"
          is-link
          @click="showProductSelector = true"
        >
          <template #icon>
            <van-icon name="plus" class="add-icon" />
          </template>
        </van-cell>
        
        <!-- 成品列表 -->
        <div v-if="assemblyForm.products.length > 0" class="product-list">
          <div
            v-for="product in assemblyForm.products"
            :key="product.id"
            class="product-item"
          >
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="product-details">
                <span class="product-code">{{ product.code }}</span>
                <span class="product-unit">{{ product.unit }}</span>
                <span class="product-price">¥{{ product.unitPrice }}</span>
              </div>
            </div>
            
            <div class="product-actions">
              <van-stepper
                v-model="product.quantity"
                min="0"
                step="1"
                @change="updateProductQuantity(product.id, $event)"
              />
              <van-button
                type="danger"
                size="mini"
                @click="removeProduct(product.id)"
              >
                删除
              </van-button>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <van-empty
          v-else
          description="暂无成品"
          image="search"
        />
      </van-cell-group>

      <!-- 成本信息 -->
      <van-cell-group title="成本信息" inset>
        <van-cell title="材料总数量" :value="totalMaterialQuantity" />
        <van-cell title="成品总数量" :value="totalProductQuantity" />
        <van-cell title="材料总成本" :value="`¥${assemblyForm.totalMaterialCost.toFixed(2)}`" />
        <van-cell title="成品总价值" :value="`¥${assemblyForm.totalProductCost.toFixed(2)}`" />
        
        <van-field
          v-model.number="assemblyForm.laborCost"
          label="人工成本"
          type="number"
          placeholder="请输入人工成本"
          @input="calculateCosts"
        />
        
        <van-field
          v-model.number="assemblyForm.overheadCost"
          label="管理费用"
          type="number"
          placeholder="请输入管理费用"
          @input="calculateCosts"
        />
        
        <van-cell 
          title="总成本" 
          :value="`¥${assemblyForm.totalCost.toFixed(2)}`"
          class="total-cost"
        />
      </van-cell-group>

      <!-- 备注信息 -->
      <van-cell-group title="备注信息" inset>
        <van-field
          v-model="assemblyForm.remark"
          label="备注"
          type="textarea"
          placeholder="请输入备注信息"
          rows="3"
          autosize
        />
      </van-cell-group>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <van-button
        type="primary"
        size="large"
        :disabled="!isFormValid"
        :loading="businessForm.loading"
        @click="submitAssembly"
      >
        提交组装单
      </van-button>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="currentDate"
        title="选择组装日期"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 仓库选择器 -->
    <van-popup v-model:show="showWarehouseSelector" position="bottom">
      <van-picker
        title="选择仓库"
        :columns="warehouseOptions"
        @confirm="onWarehouseConfirm"
        @cancel="showWarehouseSelector = false"
      />
    </van-popup>

    <!-- 组装类型选择器 -->
    <van-popup v-model:show="showAssemblyTypeSelector" position="bottom">
      <van-picker
        title="选择组装类型"
        :columns="assemblyTypeOptions"
        @confirm="onAssemblyTypeConfirm"
        @cancel="showAssemblyTypeSelector = false"
      />
    </van-popup>

    <!-- 原材料选择器 -->
    <van-popup v-model:show="showMaterialSelector" position="bottom" style="height: 80%">
      <div class="material-selector">
        <van-nav-bar
          title="选择原材料"
          left-text="取消"
          @click-left="showMaterialSelector = false"
        />
        
        <van-search
          v-model="materialSearchKeyword"
          placeholder="搜索原材料"
          @search="searchMaterials"
        />
        
        <div class="material-selector-list">
          <van-cell
            v-for="material in availableMaterials"
            :key="material.id"
            :title="material.name"
            :label="`编码: ${material.code} | 库存: ${material.stock} | 单价: ¥${material.unitPrice}`"
            is-link
            @click="selectMaterial(material)"
          />
        </div>
      </div>
    </van-popup>

    <!-- 成品选择器 -->
    <van-popup v-model:show="showProductSelector" position="bottom" style="height: 80%">
      <div class="product-selector">
        <van-nav-bar
          title="选择成品"
          left-text="取消"
          @click-left="showProductSelector = false"
        />
        
        <van-search
          v-model="productSearchKeyword"
          placeholder="搜索成品"
          @search="searchProducts"
        />
        
        <div class="product-selector-list">
          <van-cell
            v-for="product in availableProducts"
            :key="product.id"
            :title="product.name"
            :label="`编码: ${product.code} | 单价: ¥${product.unitPrice}`"
            is-link
            @click="selectProduct(product)"
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useAssemblyForm } from '@/composables/useAssemblyForm'
import type { AssemblyMaterial, AssemblyProduct, AssemblyType } from '@/types/inventory'

const router = useRouter()

// 使用组装单表单Hook
const {
  assemblyForm,
  businessForm,
  materialStockWarnings,
  showWarehouseSelector,
  showAssemblyTypeSelector,
  showMaterialSelector,
  showProductSelector,
  isFormValid,
  totalMaterialQuantity,
  totalProductQuantity,
  assemblyTypeOptions,
  generateAssemblyNo,
  calculateCosts,
  selectWarehouse,
  selectAssemblyType,
  addMaterial,
  removeMaterial,
  updateMaterialQuantity,
  addProduct,
  removeProduct,
  updateProductQuantity,
  submitAssembly,
  initForm
} = useAssemblyForm()

// 本地状态
const showDatePicker = ref(false)
const currentDate = ref(new Date())
const materialSearchKeyword = ref('')
const productSearchKeyword = ref('')

// 模拟数据
const warehouseOptions = ref([
  { text: '主仓库', value: '1' },
  { text: '分仓库A', value: '2' },
  { text: '分仓库B', value: '3' },
  { text: '临时仓库', value: '4' }
])

const availableMaterials = ref<AssemblyMaterial[]>([
  {
    id: '1',
    name: '钢材A',
    code: 'STEEL001',
    spec: '10mm*20mm',
    unit: '根',
    quantity: 10,
    unitPrice: 50,
    stock: 100,
    remark: ''
  },
  {
    id: '2',
    name: '螺丝B',
    code: 'SCREW001',
    spec: 'M6*20',
    unit: '个',
    quantity: 50,
    unitPrice: 2,
    stock: 500,
    remark: ''
  },
  {
    id: '3',
    name: '塑料片C',
    code: 'PLASTIC001',
    spec: '5mm厚',
    unit: '片',
    quantity: 5,
    unitPrice: 10,
    stock: 200,
    remark: ''
  }
])

const availableProducts = ref<AssemblyProduct[]>([
  {
    id: '101',
    name: '组装件A',
    code: 'ASSEMBLY001',
    spec: '标准规格',
    unit: '套',
    quantity: 1,
    unitPrice: 200,
    remark: ''
  },
  {
    id: '102',
    name: '组装件B',
    code: 'ASSEMBLY002',
    spec: '加强版',
    unit: '套',
    quantity: 1,
    unitPrice: 300,
    remark: ''
  }
])

// 计算属性
const isMaterialStockSufficient = (index: number): boolean => {
  const material = assemblyForm.materials[index]
  if (!material) return false

  const availableMaterial = availableMaterials.value.find(m => m.id === material.id)
  return availableMaterial ? material.quantity <= availableMaterial.stock : false
}

// 获取组装类型标签
const getAssemblyTypeLabel = (): string => {
  const option = assemblyTypeOptions.find(opt => opt.value === assemblyForm.assemblyType)
  return option ? option.label : ''
}

// 事件处理
const onDateConfirm = (value: Date) => {
  assemblyForm.assemblyDate = value.toISOString().split('T')[0]
  showDatePicker.value = false
}

const onWarehouseConfirm = ({ selectedOptions }: any) => {
  const warehouse = selectedOptions[0]
  selectWarehouse({
    id: warehouse.value,
    name: warehouse.text
  })
}

const onAssemblyTypeConfirm = ({ selectedOptions }: any) => {
  const type = selectedOptions[0]
  selectAssemblyType(type.value as AssemblyType)
}

const selectMaterial = (material: AssemblyMaterial) => {
  addMaterial({
    ...material,
    quantity: 1
  })
  showMaterialSelector.value = false
  showToast(`已添加原材料 ${material.name}`)
}

const selectProduct = (product: AssemblyProduct) => {
  addProduct({
    ...product,
    quantity: 1
  })
  showProductSelector.value = false
  showToast(`已添加成品 ${product.name}`)
}

const searchMaterials = () => {
  // 实现原材料搜索逻辑
  console.log('搜索原材料:', materialSearchKeyword.value)
}

const searchProducts = () => {
  // 实现成品搜索逻辑
  console.log('搜索成品:', productSearchKeyword.value)
}

// 生命周期
onMounted(() => {
  initForm()
})
</script>

<style scoped lang="less">
.assembly-add {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;

  .material-warnings {
    padding: 16px;

    .van-notice-bar {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .form-container {
    padding: 16px;

    .van-cell-group {
      margin-bottom: 16px;
      border-radius: 8px;
      overflow: hidden;
    }

    .add-icon {
      color: #52c41a;
      margin-right: 8px;
    }

    .material-list,
    .product-list {
      .material-item,
      .product-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #ebedf0;
        background-color: #fff;

        &:last-child {
          border-bottom: none;
        }

        .material-info,
        .product-info {
          flex: 1;

          .material-name,
          .product-name {
            font-size: 16px;
            font-weight: 500;
            color: #323233;
            margin-bottom: 4px;
          }

          .material-details,
          .product-details {
            font-size: 14px;
            color: #969799;
            margin-bottom: 8px;

            span {
              margin-right: 12px;
            }
          }

          .stock-status {
            .van-tag {
              font-size: 12px;
            }
          }
        }

        .material-actions,
        .product-actions {
          display: flex;
          align-items: center;
          gap: 12px;
        }
      }
    }

    .total-cost {
      .van-cell__value {
        color: #52c41a;
        font-weight: 600;
        font-size: 16px;
      }
    }
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background-color: #fff;
    border-top: 1px solid #ebedf0;
    z-index: 100;

    .van-button {
      width: 100%;
      background-color: #52c41a;
      border-color: #52c41a;

      &:disabled {
        background-color: #c8c9cc;
        border-color: #c8c9cc;
      }
    }
  }

  .material-selector,
  .product-selector {
    height: 100%;
    display: flex;
    flex-direction: column;

    .material-selector-list,
    .product-selector-list {
      flex: 1;
      overflow-y: auto;
      padding: 0 16px;
    }
  }
}
</style>
