<!--
  采购入库添加页面
  
  基于通用业务表单组件，实现采购入库的添加功能
  包含供应商选择、仓库选择、商品管理、质检功能等
-->

<template>
  <div class="purchase-in-add">
    <!-- 页面头部 -->
    <van-nav-bar
      title="新增采购入库"
      left-text="返回"
      left-arrow
      @click-left="handleBack"
    />

    <!-- 表单内容 -->
    <div class="form-container">
      <!-- 基础信息 -->
      <van-cell-group title="基础信息" inset>
        <!-- 入库编号 -->
        <van-field
          v-model="purchaseInForm.inNo"
          label="入库编号"
          placeholder="系统自动生成"
          readonly
          :border="false"
        />
        
        <!-- 关联采购订单 -->
        <van-field
          v-model="purchaseInForm.relatedOrderNo"
          label="关联订单"
          placeholder="请选择采购订单"
          readonly
          is-link
          :border="false"
          @click="showOrderPicker = true"
        />
        
        <!-- 供应商选择 -->
        <van-field
          v-model="purchaseInForm.supplierName"
          label="供应商"
          placeholder="请选择供应商"
          readonly
          is-link
          required
          :border="false"
          @click="showSupplierPicker = true"
        />
        
        <!-- 入库日期 -->
        <van-field
          v-model="purchaseInForm.inDate"
          label="入库日期"
          placeholder="请选择入库日期"
          readonly
          is-link
          required
          :border="false"
          @click="showDatePicker = true"
        />
        
        <!-- 采购员 -->
        <van-field
          v-model="purchaseInForm.purchaser"
          label="采购员"
          placeholder="请选择采购员"
          readonly
          is-link
          required
          :border="false"
          @click="showPurchaserPicker = true"
        />
        
        <!-- 入库仓库 -->
        <van-field
          v-model="purchaseInForm.warehouseName"
          label="入库仓库"
          placeholder="请选择入库仓库"
          readonly
          is-link
          required
          :border="false"
          @click="showWarehousePicker = true"
        />
      </van-cell-group>

      <!-- 商品信息 -->
      <van-cell-group title="商品信息" inset>
        <!-- 商品列表 -->
        <div v-if="purchaseInForm.products.length > 0" class="product-list">
          <div
            v-for="(product, index) in purchaseInForm.products"
            :key="index"
            class="product-item"
          >
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="product-spec" v-if="product.spec">{{ product.spec }}</div>
              
              <!-- 质检状态 -->
              <div class="quality-status">
                <van-tag
                  :type="getQualityStatusType(product.qualityCheckResult)"
                  size="small"
                >
                  {{ getQualityStatusText(product.qualityCheckResult) }}
                </van-tag>
              </div>
            </div>
            
            <div class="product-controls">
              <div class="quantity-control">
                <van-stepper
                  v-model="product.quantity"
                  min="1"
                  @change="handleQuantityChange(index, $event)"
                />
              </div>
              
              <div class="price-control">
                <van-field
                  v-model="product.purchasePrice"
                  type="number"
                  placeholder="单价"
                  @blur="handlePriceChange(index, Number($event.target.value))"
                />
              </div>
              
              <div class="amount">
                ¥{{ formatCurrency(product.purchasePrice * product.quantity) }}
              </div>
              
              <!-- 质检按钮 -->
              <van-button
                size="mini"
                type="primary"
                @click="handleQualityCheck(index)"
              >
                质检
              </van-button>
              
              <van-icon
                name="delete-o"
                class="delete-btn"
                @click="removeProduct(index)"
              />
            </div>
          </div>
        </div>
        
        <!-- 添加商品按钮 -->
        <van-cell
          title="添加商品"
          is-link
          :border="false"
          @click="handleAddProduct"
        >
          <template #icon>
            <van-icon name="plus" />
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 金额信息 -->
      <van-cell-group title="金额信息" inset>
        <!-- 商品小计 -->
        <van-field
          :model-value="formatCurrency(purchaseInForm.subtotal)"
          label="商品小计"
          readonly
          :border="false"
        />
        
        <!-- 优惠率 -->
        <van-field
          v-model.number="purchaseInForm.discountRate"
          label="优惠率(%)"
          type="number"
          placeholder="0"
          :border="false"
          @blur="handleDiscountChange"
        />
        
        <!-- 优惠金额 -->
        <van-field
          :model-value="formatCurrency(purchaseInForm.discountAmount)"
          label="优惠金额"
          readonly
          :border="false"
        />
        
        <!-- 最终金额 -->
        <van-field
          :model-value="formatCurrency(purchaseInForm.finalAmount)"
          label="最终金额"
          readonly
          class="final-amount"
          :border="false"
        />
      </van-cell-group>

      <!-- 质检信息 -->
      <van-cell-group title="质检信息" inset>
        <!-- 整体质检状态 -->
        <van-field
          label="质检状态"
          :border="false"
        >
          <template #input>
            <van-radio-group
              v-model="purchaseInForm.qualityCheckStatus"
              direction="horizontal"
            >
              <van-radio name="pending">待检</van-radio>
              <van-radio name="passed">合格</van-radio>
              <van-radio name="failed">不合格</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        
        <!-- 质检备注 -->
        <van-field
          v-model="purchaseInForm.qualityCheckRemark"
          label="质检备注"
          type="textarea"
          placeholder="请输入质检备注"
          rows="2"
          autosize
          :border="false"
        />
      </van-cell-group>

      <!-- 其他信息 -->
      <van-cell-group title="其他信息" inset>
        <!-- 备注 -->
        <van-field
          v-model="purchaseInForm.remark"
          label="备注"
          type="textarea"
          placeholder="请输入备注信息"
          rows="3"
          autosize
          :border="false"
        />
      </van-cell-group>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <van-button
        type="default"
        size="large"
        @click="handleSaveDraft"
        :loading="loading"
      >
        保存草稿
      </van-button>
      
      <van-button
        type="primary"
        size="large"
        @click="handleSubmit"
        :loading="submitting"
        :disabled="!isFormValid"
      >
        确认入库
      </van-button>
    </div>

    <!-- 供应商选择器 -->
    <van-popup
      v-model:show="showSupplierPicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <SupplierPicker @select="handleSupplierSelect" />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="dateValue"
        title="选择入库日期"
        @confirm="handleDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 采购员选择器 -->
    <van-popup
      v-model:show="showPurchaserPicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <PurchaserPicker @select="handlePurchaserSelect" />
    </van-popup>

    <!-- 仓库选择器 -->
    <van-popup
      v-model:show="showWarehousePicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <WarehousePicker @select="handleWarehouseSelect" />
    </van-popup>

    <!-- 采购订单选择器 -->
    <van-popup
      v-model:show="showOrderPicker"
      position="bottom"
      :style="{ height: '70%' }"
    >
      <PurchaseOrderPicker @select="handleOrderSelect" />
    </van-popup>

    <!-- 商品选择器 -->
    <van-popup
      v-model:show="showProductPicker"
      position="bottom"
      :style="{ height: '80%' }"
    >
      <ProductPicker @select="handleProductSelect" />
    </van-popup>

    <!-- 质检弹窗 -->
    <van-popup
      v-model:show="showQualityDialog"
      position="center"
      :style="{ width: '90%', borderRadius: '12px' }"
    >
      <div class="quality-dialog">
        <div class="dialog-header">
          <h3>商品质检</h3>
          <van-icon name="cross" @click="showQualityDialog = false" />
        </div>
        
        <div class="dialog-content">
          <div class="product-info">
            <div class="product-name">{{ currentQualityProduct?.name }}</div>
            <div class="product-spec">{{ currentQualityProduct?.spec }}</div>
          </div>
          
          <van-field
            label="质检结果"
            :border="false"
          >
            <template #input>
              <van-radio-group v-model="currentQualityStatus">
                <van-radio name="passed">合格</van-radio>
                <van-radio name="failed">不合格</van-radio>
                <van-radio name="pending">待检</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          
          <van-field
            v-model="currentQualityRemark"
            label="质检备注"
            type="textarea"
            placeholder="请输入质检备注"
            rows="3"
            autosize
            :border="false"
          />
        </div>
        
        <div class="dialog-actions">
          <van-button
            type="default"
            @click="showQualityDialog = false"
          >
            取消
          </van-button>
          
          <van-button
            type="primary"
            @click="handleQualityConfirm"
          >
            确认
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { usePurchaseInForm } from '@/composables/usePurchaseInForm'
import type { PurchaseProduct, Purchaser } from '@/types/purchase'
import type { Supplier, Depot } from '@/types/business'

// 组件引入
import SupplierPicker from '@/components/picker/SupplierPicker.vue'
import PurchaserPicker from '@/components/picker/PurchaserPicker.vue'
import WarehousePicker from '@/components/picker/WarehousePicker.vue'
import ProductPicker from '@/components/picker/ProductPicker.vue'
import PurchaseOrderPicker from '@/components/picker/PurchaseOrderPicker.vue'

// 路由
const router = useRouter()

// 表单逻辑
const {
  loading,
  submitting,
  purchaseInForm,
  showSupplierPicker,
  showDatePicker,
  showPurchaserPicker,
  showWarehousePicker,
  showOrderPicker,
  dateValue,
  isFormValid,
  totalQuantity,
  formatCurrency,
  validateForm,
  handleDateConfirm,
  handleSupplierSelect,
  handlePurchaserSelect,
  handleWarehouseSelect,
  handleOrderSelect,
  addProduct,
  removeProduct,
  handleQuantityChange,
  handlePriceChange,
  handleQualityCheckChange,
  handleQualityRemarkChange,
  handleDiscountChange,
  resetForm,
  initializeForm
} = usePurchaseInForm()

// 商品选择器显示状态
const showProductPicker = ref(false)

// 质检相关状态
const showQualityDialog = ref(false)
const currentQualityIndex = ref(-1)
const currentQualityProduct = ref<PurchaseProduct | null>(null)
const currentQualityStatus = ref<'passed' | 'failed' | 'pending'>('pending')
const currentQualityRemark = ref('')

/**
 * 获取质检状态类型
 */
const getQualityStatusType = (status: string) => {
  switch (status) {
    case 'passed': return 'success'
    case 'failed': return 'danger'
    default: return 'warning'
  }
}

/**
 * 获取质检状态文本
 */
const getQualityStatusText = (status: string) => {
  switch (status) {
    case 'passed': return '合格'
    case 'failed': return '不合格'
    default: return '待检'
  }
}

/**
 * 返回处理
 */
const handleBack = async () => {
  // 检查是否有未保存的更改
  if (purchaseInForm.value.products.length > 0) {
    try {
      await showConfirmDialog({
        title: '确认离开',
        message: '当前有未保存的更改，确定要离开吗？'
      })
    } catch {
      return
    }
  }
  
  router.back()
}

/**
 * 添加商品处理
 */
const handleAddProduct = () => {
  showProductPicker.value = true
}

/**
 * 商品选择处理
 */
const handleProductSelect = (product: PurchaseProduct) => {
  addProduct(product)
  showProductPicker.value = false
  showToast({ type: 'success', message: '商品已添加' })
}

/**
 * 质检处理
 */
const handleQualityCheck = (index: number) => {
  const product = purchaseInForm.value.products[index]
  currentQualityIndex.value = index
  currentQualityProduct.value = product
  currentQualityStatus.value = product.qualityCheckResult || 'pending'
  currentQualityRemark.value = product.qualityCheckRemark || ''
  showQualityDialog.value = true
}

/**
 * 质检确认
 */
const handleQualityConfirm = () => {
  if (currentQualityIndex.value >= 0) {
    handleQualityCheckChange(currentQualityIndex.value, currentQualityStatus.value)
    handleQualityRemarkChange(currentQualityIndex.value, currentQualityRemark.value)
  }
  
  showQualityDialog.value = false
  showToast({ type: 'success', message: '质检结果已保存' })
}

/**
 * 保存草稿
 */
const handleSaveDraft = async () => {
  try {
    loading.value = true
    
    // 这里调用API保存草稿
    // await savePurchaseInDraft(purchaseInForm.value)
    
    showToast({ type: 'success', message: '草稿已保存' })
  } catch (error) {
    showToast({ type: 'fail', message: '保存失败，请重试' })
  } finally {
    loading.value = false
  }
}

/**
 * 确认入库
 */
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  try {
    submitting.value = true
    
    // 这里调用API确认入库
    // await submitPurchaseIn(purchaseInForm.value)
    
    showToast({ type: 'success', message: '入库成功' })
    
    // 返回列表页
    router.replace('/purchase/in/list')
  } catch (error) {
    showToast({ type: 'fail', message: '入库失败，请重试' })
  } finally {
    submitting.value = false
  }
}

// 初始化
onMounted(() => {
  initializeForm()
})
</script>

<style lang="less" scoped>
.purchase-in-add {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;

  .form-container {
    padding: 16px;

    .van-cell-group {
      margin-bottom: 16px;
      border-radius: 8px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .product-list {
    .product-item {
      display: flex;
      align-items: flex-start;
      padding: 16px;
      border-bottom: 1px solid #ebedf0;

      &:last-child {
        border-bottom: none;
      }

      .product-info {
        flex: 1;
        min-width: 0;

        .product-name {
          font-size: 14px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }

        .product-spec {
          font-size: 12px;
          color: #969799;
          margin-bottom: 8px;
        }

        .quality-status {
          .van-tag {
            font-size: 10px;
          }
        }
      }

      .product-controls {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .quantity-control {
          .van-stepper {
            --van-stepper-button-width: 24px;
            --van-stepper-button-height: 24px;
            --van-stepper-input-width: 36px;
            --van-stepper-input-height: 24px;
          }
        }

        .price-control {
          width: 70px;

          .van-field {
            padding: 0;

            :deep(.van-field__control) {
              text-align: center;
              font-size: 11px;
            }
          }
        }

        .amount {
          font-size: 12px;
          font-weight: 500;
          color: #ee0a24;
          min-width: 50px;
          text-align: right;
        }

        .van-button {
          --van-button-mini-height: 24px;
          --van-button-mini-padding: 0 8px;
          --van-button-mini-font-size: 10px;
        }

        .delete-btn {
          color: #ee0a24;
          font-size: 16px;
          cursor: pointer;

          &:active {
            opacity: 0.7;
          }
        }
      }
    }
  }

  .final-amount {
    :deep(.van-field__label) {
      font-weight: 600;
      color: #323233;
    }

    :deep(.van-field__control) {
      font-weight: 600;
      color: #ee0a24;
      font-size: 16px;
    }
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    gap: 12px;
    padding: 16px;
    background-color: #fff;
    border-top: 1px solid #ebedf0;
    z-index: 100;

    .van-button {
      flex: 1;
      height: 44px;
      border-radius: 22px;
      font-weight: 500;

      &--default {
        background-color: #f7f8fa;
        border-color: #f7f8fa;
        color: #646566;
      }

      &--primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);

        &:active {
          transform: translateY(1px);
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
        }

        &:disabled {
          background: #c8c9cc;
          box-shadow: none;
          transform: none;
        }
      }
    }
  }

  .quality-dialog {
    padding: 20px;

    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #323233;
      }

      .van-icon {
        font-size: 18px;
        color: #969799;
        cursor: pointer;
      }
    }

    .dialog-content {
      margin-bottom: 20px;

      .product-info {
        padding: 12px;
        background-color: #f7f8fa;
        border-radius: 8px;
        margin-bottom: 16px;

        .product-name {
          font-size: 14px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }

        .product-spec {
          font-size: 12px;
          color: #969799;
        }
      }

      .van-field {
        margin-bottom: 12px;
      }
    }

    .dialog-actions {
      display: flex;
      gap: 12px;

      .van-button {
        flex: 1;
        height: 40px;
        border-radius: 20px;
      }
    }
  }

  // 弹窗样式优化
  :deep(.van-popup) {
    border-radius: 16px 16px 0 0;
  }

  :deep(.van-picker) {
    background-color: #fff;
  }

  :deep(.van-picker__toolbar) {
    border-bottom: 1px solid #ebedf0;
  }

  :deep(.van-radio-group) {
    display: flex;
    gap: 16px;
  }
}

// 响应式设计
@media (max-width: 375px) {
  .purchase-in-add {
    .product-list {
      .product-item {
        .product-controls {
          gap: 6px;

          .price-control {
            width: 60px;
          }

          .amount {
            min-width: 45px;
            font-size: 11px;
          }

          .van-button {
            --van-button-mini-padding: 0 6px;
          }
        }
      }
    }
  }
}
</style>
