<!--
  采购订单添加页面
  
  基于通用业务表单组件，实现采购订单的添加功能
  包含供应商选择、商品管理、金额计算等完整功能
-->

<template>
  <div class="purchase-order-add">
    <!-- 页面头部 -->
    <van-nav-bar
      title="新增采购订单"
      left-text="返回"
      left-arrow
      @click-left="handleBack"
    />

    <!-- 表单内容 -->
    <div class="form-container">
      <!-- 基础信息 -->
      <van-cell-group title="基础信息" inset>
        <!-- 订单编号 -->
        <van-field
          v-model="purchaseOrderForm.orderNo"
          label="订单编号"
          placeholder="系统自动生成"
          readonly
          :border="false"
        />
        
        <!-- 供应商选择 -->
        <van-field
          v-model="purchaseOrderForm.supplierName"
          label="供应商"
          placeholder="请选择供应商"
          readonly
          is-link
          required
          :border="false"
          @click="showSupplierPicker = true"
        />
        
        <!-- 订单日期 -->
        <van-field
          v-model="purchaseOrderForm.orderDate"
          label="订单日期"
          placeholder="请选择订单日期"
          readonly
          is-link
          required
          :border="false"
          @click="showDatePicker = true"
        />
        
        <!-- 采购员 -->
        <van-field
          v-model="purchaseOrderForm.purchaser"
          label="采购员"
          placeholder="请选择采购员"
          readonly
          is-link
          required
          :border="false"
          @click="showPurchaserPicker = true"
        />
        
        <!-- 预计交货日期 -->
        <van-field
          v-model="purchaseOrderForm.expectedDeliveryDate"
          label="预计交货日期"
          placeholder="请选择交货日期"
          readonly
          is-link
          :border="false"
          @click="showDeliveryDatePicker = true"
        />
      </van-cell-group>

      <!-- 商品信息 -->
      <van-cell-group title="商品信息" inset>
        <!-- 商品列表 -->
        <div v-if="purchaseOrderForm.products.length > 0" class="product-list">
          <div
            v-for="(product, index) in purchaseOrderForm.products"
            :key="index"
            class="product-item"
          >
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="product-spec" v-if="product.spec">{{ product.spec }}</div>
            </div>
            
            <div class="product-controls">
              <div class="quantity-control">
                <van-stepper
                  v-model="product.quantity"
                  min="1"
                  @change="handleQuantityChange(index, $event)"
                />
              </div>
              
              <div class="price-control">
                <van-field
                  v-model="product.purchasePrice"
                  type="number"
                  placeholder="单价"
                  @blur="handlePriceChange(index, Number($event.target.value))"
                />
              </div>
              
              <div class="amount">
                ¥{{ formatCurrency(product.purchasePrice * product.quantity) }}
              </div>
              
              <van-icon
                name="delete-o"
                class="delete-btn"
                @click="removeProduct(index)"
              />
            </div>
          </div>
        </div>
        
        <!-- 添加商品按钮 -->
        <van-cell
          title="添加商品"
          is-link
          :border="false"
          @click="handleAddProduct"
        >
          <template #icon>
            <van-icon name="plus" />
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 金额信息 -->
      <van-cell-group title="金额信息" inset>
        <!-- 商品小计 -->
        <van-field
          :model-value="formatCurrency(purchaseOrderForm.subtotal)"
          label="商品小计"
          readonly
          :border="false"
        />
        
        <!-- 优惠率 -->
        <van-field
          v-model.number="purchaseOrderForm.discountRate"
          label="优惠率(%)"
          type="number"
          placeholder="0"
          :border="false"
          @blur="handleDiscountChange"
        />
        
        <!-- 优惠金额 -->
        <van-field
          :model-value="formatCurrency(purchaseOrderForm.discountAmount)"
          label="优惠金额"
          readonly
          :border="false"
        />
        
        <!-- 税率 -->
        <van-field
          v-model.number="purchaseOrderForm.taxRate"
          label="税率(%)"
          type="number"
          placeholder="0"
          :border="false"
          @blur="handleTaxRateChange"
        />
        
        <!-- 税额 -->
        <van-field
          :model-value="formatCurrency(purchaseOrderForm.taxAmount)"
          label="税额"
          readonly
          :border="false"
        />
        
        <!-- 最终金额 -->
        <van-field
          :model-value="formatCurrency(purchaseOrderForm.finalAmount)"
          label="最终金额"
          readonly
          class="final-amount"
          :border="false"
        />
      </van-cell-group>

      <!-- 结算信息 -->
      <van-cell-group title="结算信息" inset>
        <!-- 付款账户 -->
        <van-field
          v-model="purchaseOrderForm.paymentAccount"
          label="付款账户"
          placeholder="请选择付款账户"
          :border="false"
        />
        
        <!-- 付款条件 -->
        <van-field
          v-model="purchaseOrderForm.paymentTerms"
          label="付款条件"
          placeholder="请输入付款条件"
          :border="false"
        />
        
        <!-- 预付金额 -->
        <van-field
          v-model.number="purchaseOrderForm.prepaidAmount"
          label="预付金额"
          type="number"
          placeholder="0"
          :border="false"
        />
      </van-cell-group>

      <!-- 其他信息 -->
      <van-cell-group title="其他信息" inset>
        <!-- 交货地址 -->
        <van-field
          v-model="purchaseOrderForm.deliveryAddress"
          label="交货地址"
          placeholder="请输入交货地址"
          :border="false"
        />
        
        <!-- 联系人 -->
        <van-field
          v-model="purchaseOrderForm.contactPerson"
          label="联系人"
          placeholder="请输入联系人"
          :border="false"
        />
        
        <!-- 联系电话 -->
        <van-field
          v-model="purchaseOrderForm.contactPhone"
          label="联系电话"
          placeholder="请输入联系电话"
          :border="false"
        />
        
        <!-- 备注 -->
        <van-field
          v-model="purchaseOrderForm.remark"
          label="备注"
          type="textarea"
          placeholder="请输入备注信息"
          rows="3"
          autosize
          :border="false"
        />
      </van-cell-group>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <van-button
        type="default"
        size="large"
        @click="handleSaveDraft"
        :loading="loading"
      >
        保存草稿
      </van-button>
      
      <van-button
        type="primary"
        size="large"
        @click="handleSubmit"
        :loading="submitting"
        :disabled="!isFormValid"
      >
        提交订单
      </van-button>
    </div>

    <!-- 供应商选择器 -->
    <van-popup
      v-model:show="showSupplierPicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <SupplierPicker @select="handleSupplierSelect" />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="dateValue"
        title="选择订单日期"
        @confirm="handleDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 采购员选择器 -->
    <van-popup
      v-model:show="showPurchaserPicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <PurchaserPicker @select="handlePurchaserSelect" />
    </van-popup>

    <!-- 交货日期选择器 -->
    <van-popup v-model:show="showDeliveryDatePicker" position="bottom">
      <van-date-picker
        v-model="deliveryDateValue"
        title="选择交货日期"
        @confirm="handleDeliveryDateConfirm"
        @cancel="showDeliveryDatePicker = false"
      />
    </van-popup>

    <!-- 商品选择器 -->
    <van-popup
      v-model:show="showProductPicker"
      position="bottom"
      :style="{ height: '80%' }"
    >
      <ProductPicker @select="handleProductSelect" />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { usePurchaseOrderForm } from '@/composables/usePurchaseOrderForm'
import type { PurchaseProduct, Purchaser } from '@/types/purchase'
import type { Supplier } from '@/types/business'

// 组件引入
import SupplierPicker from '@/components/picker/SupplierPicker.vue'
import PurchaserPicker from '@/components/picker/PurchaserPicker.vue'
import ProductPicker from '@/components/picker/ProductPicker.vue'

// 路由
const router = useRouter()

// 表单逻辑
const {
  loading,
  submitting,
  purchaseOrderForm,
  showSupplierPicker,
  showDatePicker,
  showPurchaserPicker,
  showDeliveryDatePicker,
  dateValue,
  deliveryDateValue,
  isFormValid,
  totalQuantity,
  formatCurrency,
  validateForm,
  handleDateConfirm,
  handleDeliveryDateConfirm,
  handleSupplierSelect,
  handlePurchaserSelect,
  addProduct,
  removeProduct,
  handleQuantityChange,
  handlePriceChange,
  handleDiscountChange,
  handleTaxRateChange,
  resetForm,
  initializeForm
} = usePurchaseOrderForm()

// 商品选择器显示状态
const showProductPicker = ref(false)

/**
 * 返回处理
 */
const handleBack = async () => {
  // 检查是否有未保存的更改
  if (purchaseOrderForm.value.products.length > 0) {
    try {
      await showConfirmDialog({
        title: '确认离开',
        message: '当前有未保存的更改，确定要离开吗？'
      })
    } catch {
      return
    }
  }
  
  router.back()
}

/**
 * 添加商品处理
 */
const handleAddProduct = () => {
  showProductPicker.value = true
}

/**
 * 商品选择处理
 */
const handleProductSelect = (product: PurchaseProduct) => {
  addProduct(product)
  showProductPicker.value = false
  showToast({ type: 'success', message: '商品已添加' })
}

/**
 * 保存草稿
 */
const handleSaveDraft = async () => {
  try {
    loading.value = true
    
    // 这里调用API保存草稿
    // await savePurchaseOrderDraft(purchaseOrderForm.value)
    
    showToast({ type: 'success', message: '草稿已保存' })
  } catch (error) {
    showToast({ type: 'fail', message: '保存失败，请重试' })
  } finally {
    loading.value = false
  }
}

/**
 * 提交订单
 */
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  try {
    submitting.value = true
    
    // 这里调用API提交订单
    // await submitPurchaseOrder(purchaseOrderForm.value)
    
    showToast({ type: 'success', message: '订单提交成功' })
    
    // 返回列表页
    router.replace('/purchase/order/list')
  } catch (error) {
    showToast({ type: 'fail', message: '提交失败，请重试' })
  } finally {
    submitting.value = false
  }
}

// 初始化
onMounted(() => {
  initializeForm()
})
</script>

<style lang="less" scoped>
.purchase-order-add {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;

  .form-container {
    padding: 16px;

    .van-cell-group {
      margin-bottom: 16px;
      border-radius: 8px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .product-list {
    .product-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #ebedf0;

      &:last-child {
        border-bottom: none;
      }

      .product-info {
        flex: 1;
        min-width: 0;

        .product-name {
          font-size: 14px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }

        .product-spec {
          font-size: 12px;
          color: #969799;
        }
      }

      .product-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .quantity-control {
          .van-stepper {
            --van-stepper-button-width: 28px;
            --van-stepper-button-height: 28px;
            --van-stepper-input-width: 40px;
            --van-stepper-input-height: 28px;
          }
        }

        .price-control {
          width: 80px;

          .van-field {
            padding: 0;

            :deep(.van-field__control) {
              text-align: center;
              font-size: 12px;
            }
          }
        }

        .amount {
          font-size: 14px;
          font-weight: 500;
          color: #ee0a24;
          min-width: 60px;
          text-align: right;
        }

        .delete-btn {
          color: #ee0a24;
          font-size: 18px;
          cursor: pointer;

          &:active {
            opacity: 0.7;
          }
        }
      }
    }
  }

  .final-amount {
    :deep(.van-field__label) {
      font-weight: 600;
      color: #323233;
    }

    :deep(.van-field__control) {
      font-weight: 600;
      color: #ee0a24;
      font-size: 16px;
    }
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    gap: 12px;
    padding: 16px;
    background-color: #fff;
    border-top: 1px solid #ebedf0;
    z-index: 100;

    .van-button {
      flex: 1;
      height: 44px;
      border-radius: 22px;
      font-weight: 500;

      &--default {
        background-color: #f7f8fa;
        border-color: #f7f8fa;
        color: #646566;
      }

      &--primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);

        &:active {
          transform: translateY(1px);
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
        }

        &:disabled {
          background: #c8c9cc;
          box-shadow: none;
          transform: none;
        }
      }
    }
  }

  // 弹窗样式优化
  :deep(.van-popup) {
    border-radius: 16px 16px 0 0;
  }

  :deep(.van-picker) {
    background-color: #fff;
  }

  :deep(.van-picker__toolbar) {
    border-bottom: 1px solid #ebedf0;
  }
}

// 响应式设计
@media (max-width: 375px) {
  .purchase-order-add {
    .product-list {
      .product-item {
        .product-controls {
          gap: 8px;

          .price-control {
            width: 60px;
          }

          .amount {
            min-width: 50px;
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
