<!--
  ERP功能网格组件
  
  用于展示各种功能模块的图标网格，支持自定义列数和样式
-->
<template>
  <div class="erp-function-grid" :class="`erp-function-grid--${columns}`">
    <div 
      v-for="item in items" 
      :key="item.id"
      class="erp-function-grid__item"
      :class="{ 'erp-function-grid__item--disabled': item.disabled }"
      @click="handleItemClick(item)"
    >
      <div class="erp-function-grid__icon" :style="{ color: item.color || defaultColor }">
        <van-icon :name="item.icon" :size="iconSize" />
      </div>
      <div class="erp-function-grid__label">{{ item.label }}</div>
      <van-badge 
        v-if="item.badge" 
        :content="item.badge" 
        class="erp-function-grid__badge"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 功能项接口
 */
interface FunctionItem {
  /** 唯一标识 */
  id: string
  /** 图标名称 */
  icon: string
  /** 显示标签 */
  label: string
  /** 图标颜色 */
  color?: string
  /** 角标数量 */
  badge?: number
  /** 跳转路径 */
  path?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 权限标识 */
  permission?: string
  /** 自定义数据 */
  data?: any
}

/**
 * 组件属性接口
 */
interface Props {
  /** 功能项列表 */
  items: FunctionItem[]
  /** 网格列数 */
  columns?: number
  /** 图标大小 */
  iconSize?: number | string
  /** 默认图标颜色 */
  defaultColor?: string
}

/**
 * 组件事件接口
 */
interface Emits {
  /** 功能项点击事件 */
  itemClick: [item: FunctionItem]
}

const props = withDefaults(defineProps<Props>(), {
  columns: 4,
  iconSize: 24,
  defaultColor: '#FF6B35'
})

const emit = defineEmits<Emits>()

/**
 * 处理功能项点击
 */
const handleItemClick = (item: FunctionItem): void => {
  if (item.disabled) {
    return
  }
  
  emit('itemClick', item)
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.erp-function-grid {
  display: grid;
  gap: var(--erp-spacing-md);
  padding: var(--erp-spacing-md);
  
  &--1 { grid-template-columns: 1fr; }
  &--2 { grid-template-columns: repeat(2, 1fr); }
  &--3 { grid-template-columns: repeat(3, 1fr); }
  &--4 { grid-template-columns: repeat(4, 1fr); }
  &--5 { grid-template-columns: repeat(5, 1fr); }
  
  // 响应式调整
  @media (max-width: @erp-mobile-xs) {
    &--4 { grid-template-columns: repeat(3, 1fr); }
    &--5 { grid-template-columns: repeat(4, 1fr); }
  }
  
  @media (min-width: @erp-tablet) {
    &--3 { grid-template-columns: repeat(4, 1fr); }
    &--4 { grid-template-columns: repeat(5, 1fr); }
  }
  
  &__item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--erp-spacing-md) var(--erp-spacing-sm);
    cursor: pointer;
    transition: all var(--erp-duration-fast) var(--erp-ease-out);
    border-radius: var(--erp-radius-md);
    min-height: 80px;
    
    &:hover {
      background: var(--erp-bg-tertiary);
      transform: translateY(-2px);
    }
    
    &:active {
      transform: translateY(0);
      background: var(--erp-border-light);
    }
    
    &--disabled {
      opacity: 0.5;
      cursor: not-allowed;
      
      &:hover {
        background: transparent;
        transform: none;
      }
    }
  }
  
  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--erp-spacing-sm);
    transition: all var(--erp-duration-fast) var(--erp-ease-out);
    
    .van-icon {
      transition: all var(--erp-duration-fast) var(--erp-ease-out);
    }
    
    .erp-function-grid__item:hover & {
      transform: scale(1.1);
    }
  }
  
  &__label {
    font-size: var(--erp-font-size-sm);
    color: var(--erp-text-primary);
    text-align: center;
    line-height: var(--erp-line-height-tight);
    font-weight: var(--erp-font-weight-medium);
    
    // 文字超长处理
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
  }
  
  &__badge {
    position: absolute;
    top: var(--erp-spacing-sm);
    right: var(--erp-spacing-sm);
    
    :deep(.van-badge) {
      .van-badge--fixed {
        top: -6px;
        right: -6px;
        min-width: 16px;
        height: 16px;
        padding: 0 4px;
        font-size: var(--erp-font-size-xs);
        line-height: 16px;
        border-radius: 8px;
      }
    }
  }
}
</style>
