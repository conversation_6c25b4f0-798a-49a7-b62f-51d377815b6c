import { type App } from 'vue'
import { 
  Button, 
  Cell, 
  CellGroup, 
  Icon, 
  Image as VanImage,
  Loading,
  Overlay,
  Toast,
  Dialog,
  Notify,
  ImagePreview,
  Lazyload
} from 'vant'

// 样式
import 'vant/lib/index.css'

const components = [
  Button,
  Cell,
  CellGroup,
  Icon,
  VanImage,
  Loading,
  Overlay,
]

const plugins = [
  Toast,
  Dialog,
  Notify,
  ImagePreview,
  Lazyload,
]

export function setupVant(app: App): void {
  components.forEach(component => {
    app.use(component)
  })
  
  plugins.forEach(plugin => {
    app.use(plugin)
  })
}