import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  const loading = ref(false)
  const theme = ref<'light' | 'dark'>('light')
  const language = ref<'zh' | 'en'>('zh')
  const networkStatus = ref(true)
  
  const setLoading = (status: boolean) => {
    loading.value = status
  }
  
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    localStorage.setItem('app_theme', theme.value)
  }
  
  const setLanguage = (lang: 'zh' | 'en') => {
    language.value = lang
    localStorage.setItem('app_language', lang)
  }
  
  const setNetworkStatus = (status: boolean) => {
    networkStatus.value = status
  }
  
  // 初始化应用状态
  const initApp = () => {
    const savedTheme = localStorage.getItem('app_theme') as 'light' | 'dark'
    const savedLanguage = localStorage.getItem('app_language') as 'zh' | 'en'
    
    if (savedTheme) {
      theme.value = savedTheme
    }
    
    if (savedLanguage) {
      language.value = savedLanguage
    }
    
    // 监听网络状态
    const updateNetworkStatus = () => {
      setNetworkStatus(navigator.onLine)
    }
    
    window.addEventListener('online', updateNetworkStatus)
    window.addEventListener('offline', updateNetworkStatus)
    updateNetworkStatus()
  }
  
  return {
    loading,
    theme,
    language,
    networkStatus,
    setLoading,
    toggleTheme,
    setLanguage,
    setNetworkStatus,
    initApp
  }
})