// 验证工具函数

export const validation = {
  // 手机号验证
  isPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },
  
  // 邮箱验证
  isEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },
  
  // 身份证验证
  isIdCard(idCard: string): boolean {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  },
  
  // 密码强度验证
  isStrongPassword(password: string): boolean {
    // 至少8位，包含大小写字母、数字和特殊字符
    const strongRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    return strongRegex.test(password)
  },
  
  // URL验证
  isUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },
  
  // 数字验证
  isNumber(value: any): boolean {
    return !isNaN(value) && !isNaN(parseFloat(value))
  },
  
  // 整数验证
  isInteger(value: any): boolean {
    return Number.isInteger(Number(value))
  },
  
  // 非空验证
  isNotEmpty(value: any): boolean {
    if (value === null || value === undefined) return false
    if (typeof value === 'string') return value.trim().length > 0
    if (Array.isArray(value)) return value.length > 0
    if (typeof value === 'object') return Object.keys(value).length > 0
    return true
  },
  
  // 长度验证
  isLength(value: string, min: number, max?: number): boolean {
    const length = value.length
    if (max !== undefined) {
      return length >= min && length <= max
    }
    return length >= min
  },
  
  // 中文验证
  isChinese(value: string): boolean {
    const chineseRegex = /^[\u4e00-\u9fa5]+$/
    return chineseRegex.test(value)
  }
}