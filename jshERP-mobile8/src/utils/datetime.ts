import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

export const datetime = {
  // 格式化日期
  format(date: Date | string | number, format = 'YYYY-MM-DD HH:mm:ss'): string {
    return dayjs(date).format(format)
  },
  
  // 相对时间
  fromNow(date: Date | string | number): string {
    return dayjs(date).fromNow()
  },
  
  // 是否是今天
  isToday(date: Date | string | number): boolean {
    return dayjs(date).isSame(dayjs(), 'day')
  },
  
  // 是否是本周
  isThisWeek(date: Date | string | number): boolean {
    return dayjs(date).isSame(dayjs(), 'week')
  },
  
  // 获取时间戳
  timestamp(date?: Date | string | number): number {
    return dayjs(date).valueOf()
  },
  
  // 日期范围
  dateRange(start: Date | string, end: Date | string): string[] {
    const startDate = dayjs(start)
    const endDate = dayjs(end)
    const dates: string[] = []
    
    let current = startDate
    while (current.isBefore(endDate) || current.isSame(endDate)) {
      dates.push(current.format('YYYY-MM-DD'))
      current = current.add(1, 'day')
    }
    
    return dates
  },
  
  // 格式化为友好的时间显示
  formatFriendly(date: Date | string | number): string {
    const now = dayjs()
    const target = dayjs(date)
    
    if (target.isSame(now, 'day')) {
      return target.format('HH:mm')
    } else if (target.isSame(now.subtract(1, 'day'), 'day')) {
      return '昨天 ' + target.format('HH:mm')
    } else if (target.isSame(now, 'year')) {
      return target.format('MM-DD HH:mm')
    } else {
      return target.format('YYYY-MM-DD HH:mm')
    }
  }
}