<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>jshERP Mobile 性能测试</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      padding: 20px;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
    
    h1 {
      color: #333;
      margin-bottom: 24px;
      text-align: center;
    }
    
    .test-section {
      margin-bottom: 32px;
      padding: 20px;
      border: 1px solid #e5e5e5;
      border-radius: 8px;
    }
    
    .test-section h2 {
      color: #666;
      margin-bottom: 16px;
      font-size: 18px;
    }
    
    .metric {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .metric:last-child {
      border-bottom: none;
    }
    
    .metric-label {
      font-weight: 500;
      color: #333;
    }
    
    .metric-value {
      font-family: 'Monaco', 'Menlo', monospace;
      color: #007acc;
      font-weight: 600;
    }
    
    .status {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 600;
    }
    
    .status.good {
      background: #e6f7ff;
      color: #1890ff;
    }
    
    .status.warning {
      background: #fff7e6;
      color: #fa8c16;
    }
    
    .status.error {
      background: #fff2f0;
      color: #f5222d;
    }
    
    .test-button {
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      margin-right: 12px;
      margin-bottom: 12px;
      transition: all 0.3s ease;
    }
    
    .test-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    }
    
    .test-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
    
    .progress-bar {
      width: 100%;
      height: 8px;
      background: #f0f0f0;
      border-radius: 4px;
      overflow: hidden;
      margin: 12px 0;
    }
    
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #1890ff, #40a9ff);
      width: 0%;
      transition: width 0.3s ease;
    }
    
    .log {
      background: #f8f8f8;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      padding: 12px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
      white-space: pre-wrap;
      color: #333;
    }
    
    .chart-container {
      width: 100%;
      height: 200px;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      margin: 12px 0;
      position: relative;
      background: white;
    }
    
    .chart-placeholder {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #999;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🚀 jshERP Mobile 性能测试工具</h1>
    
    <!-- 页面加载性能 -->
    <div class="test-section">
      <h2>📊 页面加载性能</h2>
      <div class="metric">
        <span class="metric-label">页面加载时间</span>
        <span class="metric-value" id="loadTime">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">DOM内容加载时间</span>
        <span class="metric-value" id="domContentLoaded">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">首次绘制时间</span>
        <span class="metric-value" id="firstPaint">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">首次内容绘制时间</span>
        <span class="metric-value" id="firstContentfulPaint">-</span>
      </div>
      <button class="test-button" onclick="testPageLoad()">🔄 刷新测试</button>
    </div>
    
    <!-- 内存使用 -->
    <div class="test-section">
      <h2>💾 内存使用情况</h2>
      <div class="metric">
        <span class="metric-label">已使用内存</span>
        <span class="metric-value" id="usedMemory">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">总内存</span>
        <span class="metric-value" id="totalMemory">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">内存限制</span>
        <span class="metric-value" id="memoryLimit">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">内存使用率</span>
        <span class="metric-value" id="memoryUsage">-</span>
      </div>
      <button class="test-button" onclick="testMemory()">📈 检测内存</button>
      <button class="test-button" onclick="simulateMemoryLeak()">⚠️ 模拟内存泄漏</button>
    </div>
    
    <!-- 动画性能 -->
    <div class="test-section">
      <h2>🎬 动画性能测试</h2>
      <div class="metric">
        <span class="metric-label">当前FPS</span>
        <span class="metric-value" id="currentFPS">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">平均FPS</span>
        <span class="metric-value" id="averageFPS">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">掉帧次数</span>
        <span class="metric-value" id="frameDrops">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">动画状态</span>
        <span class="status good" id="animationStatus">良好</span>
      </div>
      <div class="chart-container">
        <div class="chart-placeholder">FPS 实时图表</div>
        <canvas id="fpsChart" width="100%" height="200"></canvas>
      </div>
      <button class="test-button" onclick="startFPSTest()">▶️ 开始FPS测试</button>
      <button class="test-button" onclick="stopFPSTest()">⏹️ 停止测试</button>
      <button class="test-button" onclick="stressTestAnimation()">🔥 压力测试</button>
    </div>
    
    <!-- 网络性能 -->
    <div class="test-section">
      <h2>🌐 网络性能</h2>
      <div class="metric">
        <span class="metric-label">网络延迟</span>
        <span class="metric-value" id="networkLatency">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">资源加载时间</span>
        <span class="metric-value" id="resourceLoadTime">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">连接类型</span>
        <span class="metric-value" id="connectionType">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">下载速度</span>
        <span class="metric-value" id="downloadSpeed">-</span>
      </div>
      <button class="test-button" onclick="testNetwork()">🔍 网络测试</button>
      <button class="test-button" onclick="testAPIPerformance()">⚡ API性能测试</button>
    </div>
    
    <!-- Bundle 分析 -->
    <div class="test-section">
      <h2>📦 Bundle 分析</h2>
      <div class="metric">
        <span class="metric-label">JavaScript 大小</span>
        <span class="metric-value" id="jsSize">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">CSS 大小</span>
        <span class="metric-value" id="cssSize">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">图片资源大小</span>
        <span class="metric-value" id="imageSize">-</span>
      </div>
      <div class="metric">
        <span class="metric-label">总资源大小</span>
        <span class="metric-value" id="totalSize">-</span>
      </div>
      <button class="test-button" onclick="analyzeBundleSize()">📊 分析Bundle</button>
    </div>
    
    <!-- 测试日志 -->
    <div class="test-section">
      <h2>📝 测试日志</h2>
      <div class="log" id="testLog">等待测试开始...</div>
      <button class="test-button" onclick="clearLog()">🗑️ 清空日志</button>
      <button class="test-button" onclick="exportResults()">💾 导出结果</button>
    </div>
    
    <!-- 综合测试 -->
    <div class="test-section">
      <h2>🎯 综合性能测试</h2>
      <div class="progress-bar">
        <div class="progress-fill" id="overallProgress"></div>
      </div>
      <div class="metric">
        <span class="metric-label">综合评分</span>
        <span class="metric-value" id="overallScore">-</span>
      </div>
      <button class="test-button" onclick="runFullTest()" id="fullTestBtn">🚀 运行完整测试</button>
    </div>
  </div>

  <script>
    // 性能测试工具类
    class PerformanceTester {
      constructor() {
        this.fpsData = []
        this.isTestingFPS = false
        this.testResults = {}
        this.log('性能测试工具初始化完成')
      }
      
      log(message) {
        const timestamp = new Date().toLocaleTimeString()
        const logElement = document.getElementById('testLog')
        logElement.textContent += `[${timestamp}] ${message}\n`
        logElement.scrollTop = logElement.scrollHeight
      }
      
      formatBytes(bytes) {
        const sizes = ['B', 'KB', 'MB', 'GB']
        if (bytes === 0) return '0 B'
        const i = Math.floor(Math.log(bytes) / Math.log(1024))
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
      }
      
      formatTime(ms) {
        if (ms < 1000) return `${Math.round(ms)}ms`
        return `${(ms / 1000).toFixed(2)}s`
      }
    }
    
    const tester = new PerformanceTester()
    
    // 页面加载性能测试
    function testPageLoad() {
      tester.log('开始页面加载性能测试...')
      
      if ('performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0]
        
        if (navigation) {
          const loadTime = navigation.loadEventEnd - navigation.loadEventStart
          const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart
          
          document.getElementById('loadTime').textContent = tester.formatTime(loadTime)
          document.getElementById('domContentLoaded').textContent = tester.formatTime(domContentLoaded)
          
          tester.testResults.loadTime = loadTime
          tester.testResults.domContentLoaded = domContentLoaded
        }
        
        // 获取绘制时间
        const paintEntries = performance.getEntriesByType('paint')
        paintEntries.forEach(entry => {
          if (entry.name === 'first-paint') {
            document.getElementById('firstPaint').textContent = tester.formatTime(entry.startTime)
            tester.testResults.firstPaint = entry.startTime
          } else if (entry.name === 'first-contentful-paint') {
            document.getElementById('firstContentfulPaint').textContent = tester.formatTime(entry.startTime)
            tester.testResults.firstContentfulPaint = entry.startTime
          }
        })
        
        tester.log('页面加载性能测试完成')
      } else {
        tester.log('浏览器不支持 Performance API')
      }
    }
    
    // 内存测试
    function testMemory() {
      tester.log('开始内存使用测试...')
      
      if ('performance' in window && 'memory' in performance) {
        const memory = performance.memory
        const used = memory.usedJSHeapSize
        const total = memory.totalJSHeapSize
        const limit = memory.jsHeapSizeLimit
        const usage = ((used / limit) * 100).toFixed(2)
        
        document.getElementById('usedMemory').textContent = tester.formatBytes(used)
        document.getElementById('totalMemory').textContent = tester.formatBytes(total)
        document.getElementById('memoryLimit').textContent = tester.formatBytes(limit)
        document.getElementById('memoryUsage').textContent = `${usage}%`
        
        tester.testResults.memory = { used, total, limit, usage }
        tester.log(`内存使用: ${tester.formatBytes(used)} / ${tester.formatBytes(limit)} (${usage}%)`)
      } else {
        tester.log('浏览器不支持内存监控')
      }
    }
    
    // FPS测试
    function startFPSTest() {
      if (tester.isTestingFPS) return
      
      tester.isTestingFPS = true
      tester.fpsData = []
      tester.log('开始FPS测试...')
      
      let frameCount = 0
      let lastTime = performance.now()
      let fpsSum = 0
      let frameDrops = 0
      
      function measureFPS(currentTime) {
        if (!tester.isTestingFPS) return
        
        const delta = currentTime - lastTime
        const fps = 1000 / delta
        
        if (fps < 55) frameDrops++
        
        frameCount++
        fpsSum += fps
        tester.fpsData.push(fps)
        
        // 更新显示
        document.getElementById('currentFPS').textContent = Math.round(fps)
        document.getElementById('averageFPS').textContent = Math.round(fpsSum / frameCount)
        document.getElementById('frameDrops').textContent = frameDrops
        
        // 更新状态
        const status = document.getElementById('animationStatus')
        if (fps >= 55) {
          status.textContent = '优秀'
          status.className = 'status good'
        } else if (fps >= 30) {
          status.textContent = '良好'
          status.className = 'status warning'
        } else {
          status.textContent = '较差'
          status.className = 'status error'
        }
        
        lastTime = currentTime
        requestAnimationFrame(measureFPS)
      }
      
      requestAnimationFrame(measureFPS)
    }
    
    function stopFPSTest() {
      tester.isTestingFPS = false
      tester.log('FPS测试已停止')
    }
    
    // 压力测试动画
    function stressTestAnimation() {
      tester.log('开始动画压力测试...')
      
      // 创建大量动画元素
      const container = document.body
      const elements = []
      
      for (let i = 0; i < 100; i++) {
        const div = document.createElement('div')
        div.style.cssText = `
          position: fixed;
          width: 10px;
          height: 10px;
          background: hsl(${Math.random() * 360}, 70%, 50%);
          border-radius: 50%;
          pointer-events: none;
          z-index: 9999;
          left: ${Math.random() * window.innerWidth}px;
          top: ${Math.random() * window.innerHeight}px;
          animation: stress-test ${1 + Math.random() * 2}s infinite linear;
        `
        container.appendChild(div)
        elements.push(div)
      }
      
      // 添加动画样式
      if (!document.getElementById('stress-test-style')) {
        const style = document.createElement('style')
        style.id = 'stress-test-style'
        style.textContent = `
          @keyframes stress-test {
            0% { transform: translate(0, 0) rotate(0deg) scale(1); }
            25% { transform: translate(100px, 100px) rotate(90deg) scale(1.5); }
            50% { transform: translate(-100px, 100px) rotate(180deg) scale(0.5); }
            75% { transform: translate(-100px, -100px) rotate(270deg) scale(1.2); }
            100% { transform: translate(0, 0) rotate(360deg) scale(1); }
          }
        `
        document.head.appendChild(style)
      }
      
      // 5秒后清理
      setTimeout(() => {
        elements.forEach(el => el.remove())
        tester.log('动画压力测试完成')
      }, 5000)
    }
    
    // 网络测试
    function testNetwork() {
      tester.log('开始网络性能测试...')
      
      // 测试网络延迟
      const startTime = performance.now()
      fetch(window.location.href + '?t=' + Date.now(), { method: 'HEAD' })
        .then(() => {
          const latency = performance.now() - startTime
          document.getElementById('networkLatency').textContent = tester.formatTime(latency)
          tester.testResults.networkLatency = latency
          tester.log(`网络延迟: ${tester.formatTime(latency)}`)
        })
        .catch(err => {
          tester.log('网络测试失败: ' + err.message)
        })
      
      // 获取连接信息
      if ('connection' in navigator) {
        const connection = navigator.connection
        document.getElementById('connectionType').textContent = connection.effectiveType || 'unknown'
        document.getElementById('downloadSpeed').textContent = connection.downlink ? `${connection.downlink} Mbps` : 'unknown'
      }
      
      // 分析资源加载时间
      const resources = performance.getEntriesByType('resource')
      if (resources.length > 0) {
        const totalTime = resources.reduce((sum, resource) => {
          return sum + (resource.responseEnd - resource.requestStart)
        }, 0)
        const avgTime = totalTime / resources.length
        document.getElementById('resourceLoadTime').textContent = tester.formatTime(avgTime)
        tester.testResults.resourceLoadTime = avgTime
      }
    }
    
    // API性能测试
    function testAPIPerformance() {
      tester.log('开始API性能测试...')
      
      const testAPIs = [
        { name: 'JSON测试', url: 'data:application/json,{"test":true}' },
        { name: '图片测试', url: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7' }
      ]
      
      testAPIs.forEach(api => {
        const startTime = performance.now()
        fetch(api.url)
          .then(response => response.blob())
          .then(() => {
            const duration = performance.now() - startTime
            tester.log(`${api.name}: ${tester.formatTime(duration)}`)
          })
          .catch(err => {
            tester.log(`${api.name} 失败: ${err.message}`)
          })
      })
    }
    
    // Bundle大小分析
    function analyzeBundleSize() {
      tester.log('开始Bundle大小分析...')
      
      const resources = performance.getEntriesByType('resource')
      let jsSize = 0, cssSize = 0, imageSize = 0, totalSize = 0
      
      resources.forEach(resource => {
        const size = resource.transferSize || 0
        totalSize += size
        
        if (resource.name.includes('.js')) {
          jsSize += size
        } else if (resource.name.includes('.css')) {
          cssSize += size
        } else if (resource.name.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) {
          imageSize += size
        }
      })
      
      document.getElementById('jsSize').textContent = tester.formatBytes(jsSize)
      document.getElementById('cssSize').textContent = tester.formatBytes(cssSize)
      document.getElementById('imageSize').textContent = tester.formatBytes(imageSize)
      document.getElementById('totalSize').textContent = tester.formatBytes(totalSize)
      
      tester.testResults.bundleSize = { jsSize, cssSize, imageSize, totalSize }
      tester.log(`Bundle分析完成 - 总大小: ${tester.formatBytes(totalSize)}`)
    }
    
    // 模拟内存泄漏
    function simulateMemoryLeak() {
      tester.log('模拟内存泄漏...')
      
      const leakArray = []
      const interval = setInterval(() => {
        // 创建大量对象但不释放
        for (let i = 0; i < 1000; i++) {
          leakArray.push(new Array(1000).fill(Math.random()))
        }
        
        if (leakArray.length > 10000) {
          clearInterval(interval)
          tester.log('内存泄漏模拟完成，请检查内存使用情况')
        }
      }, 100)
      
      setTimeout(() => {
        clearInterval(interval)
        leakArray.length = 0 // 清理内存
        tester.log('内存已清理')
      }, 3000)
    }
    
    // 运行完整测试
    async function runFullTest() {
      const btn = document.getElementById('fullTestBtn')
      btn.disabled = true
      btn.textContent = '测试中...'
      
      const progress = document.getElementById('overallProgress')
      let currentProgress = 0
      
      const updateProgress = (percent) => {
        currentProgress = percent
        progress.style.width = `${percent}%`
      }
      
      tester.log('开始完整性能测试...')
      
      // 测试步骤
      const tests = [
        { name: '页面加载测试', func: testPageLoad },
        { name: '内存测试', func: testMemory },
        { name: '网络测试', func: testNetwork },
        { name: 'Bundle分析', func: analyzeBundleSize }
      ]
      
      for (let i = 0; i < tests.length; i++) {
        const test = tests[i]
        tester.log(`执行 ${test.name}...`)
        
        try {
          await new Promise(resolve => {
            test.func()
            setTimeout(resolve, 1000) // 等待测试完成
          })
          updateProgress(((i + 1) / tests.length) * 100)
        } catch (error) {
          tester.log(`${test.name} 失败: ${error.message}`)
        }
      }
      
      // 计算综合评分
      const score = calculateOverallScore()
      document.getElementById('overallScore').textContent = `${score}/100`
      
      btn.disabled = false
      btn.textContent = '🚀 运行完整测试'
      tester.log('完整性能测试完成！')
    }
    
    // 计算综合评分
    function calculateOverallScore() {
      let score = 100
      const results = tester.testResults
      
      // 页面加载时间评分 (30分)
      if (results.loadTime) {
        if (results.loadTime > 3000) score -= 15
        else if (results.loadTime > 1000) score -= 8
        else if (results.loadTime > 500) score -= 3
      }
      
      // 首次内容绘制评分 (20分)
      if (results.firstContentfulPaint) {
        if (results.firstContentfulPaint > 2000) score -= 10
        else if (results.firstContentfulPaint > 1000) score -= 5
        else if (results.firstContentfulPaint > 500) score -= 2
      }
      
      // 内存使用评分 (20分)
      if (results.memory && results.memory.usage) {
        const usage = parseFloat(results.memory.usage)
        if (usage > 80) score -= 15
        else if (usage > 60) score -= 8
        else if (usage > 40) score -= 3
      }
      
      // 网络延迟评分 (15分)
      if (results.networkLatency) {
        if (results.networkLatency > 1000) score -= 10
        else if (results.networkLatency > 500) score -= 5
        else if (results.networkLatency > 200) score -= 2
      }
      
      // Bundle大小评分 (15分)
      if (results.bundleSize && results.bundleSize.totalSize) {
        const sizeMB = results.bundleSize.totalSize / (1024 * 1024)
        if (sizeMB > 5) score -= 10
        else if (sizeMB > 3) score -= 5
        else if (sizeMB > 1) score -= 2
      }
      
      return Math.max(0, Math.round(score))
    }
    
    // 清空日志
    function clearLog() {
      document.getElementById('testLog').textContent = ''
    }
    
    // 导出结果
    function exportResults() {
      const results = {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        testResults: tester.testResults,
        overallScore: document.getElementById('overallScore').textContent
      }
      
      const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `performance-test-${Date.now()}.json`
      a.click()
      URL.revokeObjectURL(url)
      
      tester.log('测试结果已导出')
    }
    
    // 页面加载完成后自动运行基础测试
    window.addEventListener('load', () => {
      setTimeout(() => {
        testPageLoad()
        testMemory()
        testNetwork()
      }, 1000)
    })
  </script>
</body>
</html>
